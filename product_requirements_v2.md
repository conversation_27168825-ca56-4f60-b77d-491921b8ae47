# Overview

Product name: Voji

Vision: Voji is a mobile / web app that helps the user quickly capture ideas with their voice and turn them into complete stories, powered by Generative AI.

# Data models

## Ideabooks

An ideabook is a collection of ideas.

Ideabook has the following associated data:
* Short name: Usually summarized by AI. User can edit it manually.
* Color: color of the ideabook for flexible categorization.
* Lock: whether the ideabook is locked.
* Created time: when the ideabook is created.

Ideabook is the container of the following data models:
* Idea - user input captured by voice recording or manual input text.
* Note - LLM answer saved by user as a note. Note has a title and text body.
* Chat - user can chat with <PERSON>M about their ideabook. Ideas will be used as context input to LLM conversation. Chat history is stored on user local device for fast loading.

### Color system

The Color system is a flexible way to allow user categorize their ideabooks.

Voji supports 6 colors in total. When user creates a new ideabook, Voji will use AI to determine the color based on the content of the ideabook. The color is determined by the following rules:
* `red`: work, professional, etc.
* `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
* `blue`: personal growth, health, time management, etc.
* `yellow`: inspiration, diary, daily log, journal, etc.
* `orange`: travel, planning, birthday, parties, wedding, etc.
* `purple`: everything else but try to much other colors if possible.

But user can always change the color manually and decides how they want to categorize their ideabooks by color.

### Lock

An ideabook can be locked. User can click the lock button to lock / unlock an ideabook. If the user tries to lock a ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode. A single passcode is used to lock / unlock all ideabooks. Voji doesn't support per-ideabook passcode.

When an ideabook is locked, the following actions require a passcode:
* Access the ideabook content, to see all the ideas, notes, chat in that ideabook.
* Unlock the ideabook.
* User needs to type in the passcode to lock an ideabook except for the very first time when they lock any ideabook (at that they're prompted to enter a new passcode).

The following actions do not require a passcode:
* Ideabook short name is still visible in the Ideabooks list page.
* Add new idea in Ideabooks list page.
* Change color.

In the Ideabooks list page, there is no obvious indication of which Ideabook is locked to respect user's privacy. User can only tell if an ideabook is locked in the expanded context menu, based on if the lock or unlock button is shown.
