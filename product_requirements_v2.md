# Overview

Product name: Voji

Vision: Voji is a mobile / web app that helps the user quickly capture ideas with their voice and turn them into complete stories, powered by Generative AI.

# Data models

## Ideabook

An ideabook is a collection of ideas.

Ideabook has the following associated data:
* Short name: Usually summarized by AI. User can edit it manually.
* Color: color of the ideabook for flexible categorization.
* Lock: whether the ideabook is locked.
* Created time: when the ideabook is created.

Ideabook is the container of the following data models:
* Idea - user input captured by voice recording or manual input text.
* Note - LLM answer saved by user as a note. Note has a title and text body.
* Chat - user can chat with <PERSON>M about their ideabook. Ideas will be used as context input to LLM conversation. Chat history is stored on user local device for fast loading.

### Color system

The Color system is a flexible way to allow user categorize their ideabooks.

Voji supports 6 colors in total. When user creates a new ideabook, Voji will use AI to determine the color based on the content of the ideabook. The color is determined by the following rules:
* `red`: work, professional, etc.
* `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
* `blue`: personal growth, health, time management, etc.
* `yellow`: inspiration, diary, daily log, journal, etc.
* `orange`: travel, planning, birthday, parties, wedding, etc.
* `purple`: everything else but try to much other colors if possible.

But user can always change the color manually and decides how they want to categorize their ideabooks by color.

### Lock

An ideabook can be locked. User can click the lock button to lock / unlock an ideabook. If the user tries to lock a ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode. A single passcode is used to lock / unlock all ideabooks. Voji doesn't support per-ideabook passcode.

When an ideabook is locked, the following actions require a passcode:
* Access the ideabook content, to see all the ideas, notes, chat in that ideabook.
* Unlock the ideabook.
* User needs to type in the passcode to lock an ideabook except for the very first time when they lock any ideabook (at that they're prompted to enter a new passcode).

The following actions do not require a passcode:
* Ideabook short name is still visible in the Ideabooks list page.
* Add new idea in Ideabooks list page.
* Change color.

In the Ideabooks list page, there is no obvious indication of which Ideabook is locked to respect user's privacy. The only visual indication is in the context menu, based on if the lock or unlock button is shown.

The passcode is not stored in cloud. An encrypted salt (a fingerprint of the passcode) is stored in Firestore to support multi devices passcode verification for locked ideabooks. Voji backend does not have access to the passcode nor provides any way to retrieve it. User is clearly reminded that they are responsible for keeping the passcode safe and Voji cannot help retrieve it if lost.

## Idea

An idea is a user input captured by voice recording or manual input text. An idea always belong to one of the Ideabooks.

Idea has the following associated data:
* Content: the transcribed, LLM-refined text, or user manually edited text.
* Creation timestamp: when this idea is created initially.

User can freely create, edit, delete any idea in any ideabook they own. User can manually reorder the ideas within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Note

A note is an LLM answer saved by user as a note. A note always belong to one of the Ideabooks.

Note has the following associated data:
* Title: the user's original prompt.
* Content: the LLM response.
* Creation timestamp: when this note is created initially.

User can freely save note, edit the title, delete any note in any ideabook they own. User can manually reorder the notes within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Chat

A chat is a conversation with LLM about an ideabook. A chat always belong to one of the Ideabooks. Chat is a list of messages between user and LLM. Chat is saved locally on user's device only. Chat is not synced to cloud. Chat is not preserved when user switch device or reinstall the app.

# UI Features

## Welcome page

This is the first page user sees when they open the app for the first time. The purpose of the welcome page is to ask user sign in with their social account before they can continue to use the app. Google authentication is the only supported authentication method for now.

The welcome page has the following elements:
* Logo: Voji in Pacifico font.
* Slogan: "Ideas Spoken. Stories Born". in signika google font.
* Sign in to Continue, in Afacad font.
* Only 1 sign option: "Sign in with Google"
* Link to terms of service and privacy policy. User is clearly reminded that by signing in, they agree to the terms of service and privacy policy.

## Ideabook list page

This is the main page of the app.

A top bar:
* To the left: The logo "Voji"
* To the right: a user profile picture as a button to open a menu.

The menu supports the following actions:
* Upgrade to Voji PRO
* Settings
* Filter by color
* Sort by created time: asc / desc
* Group by color

A list of ideabooks. Each ideabook in the list shows several elements:
* Color: color of the ideabook for flexible categorization.
* Short name: Usually summarized by AI. User can edit it manually.
* A microphone button: click it will trigger recording of new idea that will be saved into the ideabook.
* Swipe to left will show more context buttons.
* Swipe to right will show color selection.

The expanded context buttons:
* The same microphone button.
* A pencil button to edit the ideabook short name.
* A lock / unlock button lock / unlock the ideabook, depending on if the ideabook is locked already.
* A trash bin button to delete the ideabook (triggers a confirmation dialog before deletion).

The color selection:
* A list of colors to choose from.

At the bottom of the ideabook list page is the "New Ideabook" button. Click it will trigger voice recording to create new ideabook.

## Settings page

The settings page is accessible from the menu in the top bar of the ideabooks list page.