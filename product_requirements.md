# Overview

Product name: Voji

Vision: A mobile app, helps quickly capture dialy fragmented ideas, usually in the form of voice memos (thus the name "voji"), and turn them into complete stories, powered by Gen AI.


# Features and Data models

All data is stored locally on user device. Should be persisted so that when app is closed and reopened, the data is still there.

## Ideabook

A Ideabook is a collection of ideas.

Ideabook has the following data associated:

* Short name: Usually summarized by LLM. User can edit it.
* Color: color of the ideabook for flexible categorization.
* Lock: whether the ideabook is locked.

Ideabook is the container of the following data models:

* Idea - user input captured by voice recording or manual input text.
* Note - LLM answer saved by user as a note. Note has a title and text body.
* Chat - user can chat with <PERSON>M about their ideabook. Ideas will be used as context input to LLM conversation. Chat history is stored on user local device for fast loading.

### Color system

The Color system is a flexible way to allow user categorize their ideabooks.

There are 6 colors in total:
* Red - ff595e
* Orange - ff924c
* Yellow - ffca3a
* Green - 8ac926
* Blue - 1982c4
* Purple - 6a4c93

https://coolors.co/palette/ff595e-ff924c-ffca3a-8ac926-1982c4-6a4c93

A ideabook can have no color. It will be displayed as gray-ish color.

### Lock

A ideabook can be locked. User can click the lock button to lock a ideabook. If the user tries to lock a ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode (or use face id, fingerprint).

When a ideabook is locked, the following actions require a passcode:

* access the ideabook detail page, to see all the ideas in that ideabook.
* unlock the ideabook.
* delete the ideabook

The following actions do not require a passcode:

* Ideabook short name is still visible in the Ideabooks list page.
* Add new idea in Ideabooks list page.
* Change color.

In the Ideabooks list page, there is no obvious indication of which Ideabook is locked. User can only tell in the expanded context menu, based on if the lock or unlock button is shown.


### Ideabook creation

User clicks the "New Ideabook" button in the Ideabooks page. User record a short voice memo of what the Ideabook is intended to do. Use LLM API to transcribe the voice into text. And then use LLM to refine and summarize the text into the Ideabook short name.

When the "New Ideabook" button is clicked, the bottom panel turns into a voice recorder mode. While user speaks, the bottom panel shows a waveform of the recording. User can click the check mark button to finish the recording. The waveform updates realtime as user speaks. The UI is minimlist for the audio recording. It has only three parts: a 'X' icon button to cancel, a waveform of the recording, and a check mark icon button to finish the recording. All the three elements are in the same row as the "New Ideabook" button. Do not include any transport control like stop/pause/play etc.


## Idea

Ideas are the inputs user created. User can capture their ideas by voice recording or manully input text. An idea always belong to one of the Ideabooks. A Ideabook can have unlimited number of ideas.


An idea has the following data associated:

* The original voice recording or text input by the user.
* The transcribed, LLM-refined text of the idea.
* Timestamp: when this idea is created initially.

## Note

Notes are LLM answers that user picks to save. Note has a note title and note text. The title stores the user's original prompt. The text stores the LLM response.


# UI

## Overall style

Minimalism. Flat design.


## Ideabooks list page

This is the main page of the app. A list of ideabooks. Each ideabook in the list shows several elements:

* The color indicator
* The short name
* A microphone button: click it will trigger recording of new idea that will be saved into the ideabook.
* Swap to left will show more context buttons.

The expanded context buttons:
* The same microphone button.
* A pencil button to edit the ideabook short name.
* A lock / unlock button lock / unlock the ideabook, depending on if the ideabook is locked already.
* A trash bin button to delete the ideabook (triggers a confirmation dialog before deletion).

Click the color indicator can change the color of the ideabook. Click the color indicator changes the ideabook row to be "color picking mode". the whole row shows a selection of colors. Click a color will change the color of the ideabook.

### Top bar

The top bar of the ideabooks list page is:
To the left: The logo "Voji"
To the right: a three-line button to open a context menu.

The context menu includes:
* Switch dark / light theme
* Change passcode
* Filter by color
* Sort by created time: asc / desc
* Group by color

Filter by color option shows a list of colors. Click a color will filter the ideabooks list to show only ideabooks with that color.

The top bar is floating while scrolling the ideabooks.

### Search bar

Below top bar is the search bar. The search bar will filter ideabook by keyword match. Search bar is not floating. It scrolls away as user scrolls the ideabooks.

### Bottom bar

The bottom bar is fixed at the bottom position. It has a button "New Ideabook". Click the button to trigger voice recording. User describes their intention for this ideabook. The voice is transcribed to text. LLM refine the text to get the short name.

### Actions

#### Voice Recording

Clicks the microphone button to trigger the voice recording mode. That ideabook row turns into recoding mode.
In recording mode, it shows a waveform of user recording, to the right is a check mark button to finish the recording.
Once recording is persisted successfully, a temporary notification (1 second) is shown inside the ideabook row "Idea saved". And then the ideabook goes back to regular mode.

#### Edit ideabook short name

Clicks the pencil button to edit ideabook name. That ideabook row turns into a single-row text input box and a check mark button on the right hand side to save to ideabook name. Click anywhere outside the text input box will discard the change. When saved, the ideabook row goes back to regular mode. Show a notification once saved successfully.


#### Lock

A ideabook can be locked. User can click the lock button to lock a ideabook. If the user tries to lock any ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode. The same passcode is used to unlock all ideabooks that are locked.

The passcode screen should be similar to iPhone passcode screen: show a 4x3 grid of numbers. User can enter the passcode by tapping the numbers. The passcode can be 4-6 digits long. To confirm the passcode, user types the same passcode again. The same passcode screen is used to unlock any locked ideabook and to disable the lock state of any ideabook.

If the ideabook is not locked, it shows a empty unlocked icon.
If the ideabook is locked, it shows a solid locked icon.

When user tries to open a locked ideabook (going into detail page), the passcode screen is shown. User has to enter the correct passcode to unlock the ideabook.

To disable the lock state of a ideabook, user has to click the lock button in the context menu, enter the correct passcode, and then confirm to disable the lock state.

## Ideabook detail page

The top bar shows the ideabook short name (aligned to right) and a back arrow (aligned to left) to navigate back to ideabooks list page.

The detail page is split to three tabs:

* Ideas
* Chat
* Notes

The tab bar is fixed at the bottom. The tab bar color is the same color as the ideabook color.

### Ideas tab

Shows the list of ideas. Swipe to shows context menu:
* a delete button to delete a idea. 

Click the idea row to enter edit mode. In edit mode, the idea row turns into a multi-line text input box. User can edit the text. There are three buttons below the text input box:
Aligned to left:
* A mic button for recording new text to be appended to the end of the idea.
Clicking on the mic button turns the three-button row into a recording mode. Similar to other recording panels. It shows the close button, waveform and checkmark button to finish recording.
Aligned to right:
* Cancel: exit edit mode without saving.
* Save: save the edited text.

The bottom section shows a "New Idea" button. It triggers the voice recording to create new idea.

The ideas can be reordered by drag-n-drop. When long click on a idea, it enters drag-n-drop mode. The selected row turns slightly bigger. User can drag the row to reorder. CRITICAL: long click on a idea row should not enter edit mode.
The idea sort order is stored in firestore so that the order is preserved when user revisits the ideas tab.

When a new idea is created, it is added to the top of the list.

### Chat tab

Look and feel like a common LLM chat UI.

At bottom is a chat box ("Chat your ideabook"). With a mic icon to trigger voice chat.

Messages are ordered from top to bottom. User message is shown aligned to the right with light gray background color. LLM response takes the full width of the screen without background.

Below LLM response, there are two buttons:
* "Save as Note" - save the current response to Notes
* "Copy" - copy the response to clipboard.

There is a Mic button besides the input chat box. When clicking the mic button, the chat box row turns into a voice recording mode (similar to the recording mode in the ideabooks list page). It shows a waveform of the recording, to the right is a check mark button to finish the recording, and to the left is a 'x' close button to cancel the recording.

Hitting enter key while typing in the chat box don't send the message. It just inserts a new line. User can send the message by clicking the send button besides the chat box.

### Notes tab

A list of all saved notes.

Swipe on a note to show the context menu: a delete button to delete the note.

Click on the Note to open the Note detail page.

If there is only one note, the notes list page is skipped, and the note detail page is shown directly. Only show the notes list page when there is more than one note.

The note detail page:
* The note title (user prompt) in gray background color. similar buble style as the user message in the chat tab. but takes the full width.
* The note text (LLM response) in Markdown format.
* Buttons area:
  ** Aligned to left: a regenerate button to regenerate the note text by sending the note title (user prompt) to LLM again.
  ** Aligned to right: a trash bin button to delete the entire note.

Clicking the note title (user prompt) anywhere inside the bubble area changes the note title text to be an editable text input box. User can edit the text. There are several buttons below the note title bubble area:
Align to left:
* Mic button to record voice to be transcribed and appended to the end of the note title text.
Align to right:
* Cancel: exit edit mode without saving.
* Save: save the edited text.
The button line is similar to the button line in the idea edit mode.

### Sorting order.

The sorting order logic discussed in this section applies to both ideas and notes. But for the sake of simple, we will use "ideas" as the example.

User triggers drag-and-drop mode to reorder the ideas by long pressing on a idea row. When long press on a idea row, the row turns slightly bigger and brighter (depending on theme) to indicate the row is selected and ready to be draged for reordering. Long press on a idea row does not enter the idea edit mode.

The sorting logic follows the below algorithm.

```
## Algorithm: Hybrid Sort Order with Fractional Indexing for Ideas

**Objective:**
To provide a flexible sorting mechanism for a list of ideas within an ideabook. The system supports:
1.  **Default Sorting:** Ideas are sorted by creation time, with the newest idea appearing at the top.
2.  **Manual User Sorting:** Users can reorder ideas via drag-and-drop.
3.  **Efficiency:** Minimize Firestore write operations and data storage, primarily by only modifying the document of the idea being moved.

**Core Data Representation:**
Each idea document utilizes two potential values for determining its sort position:

1.  `created_at` (Timestamp): A standard Firestore metadata field indicating when the idea document was created (e.g., milliseconds since epoch). This is an immutable value and serves as the default sort key.
2.  `s` (Float): An optional field stored in the idea document. This field is only present if the idea has been manually reordered by the user. It stores a floating-point number representing its user-defined sort order.

**Effective Sort Value:**
To determine an idea's actual position in the sorted list, we use its "effective sort value":
*   `effective_sort_value(idea) = idea.s ?? idea.created_at`
    *(This means: if `idea.s` exists and is not null, use `idea.s`; otherwise, use `idea.created_at`.)*

**Sorting Rule:** Ideas are sorted in **descending order** based on their `effective_sort_value`. A higher value means the idea is ranked higher (appears closer to the top).

**Algorithm Details:**

1.  **Default State & New Ideas:**
    *   When an idea is newly created, it does **not** have an `s` field.
    *   Its `effective_sort_value` is its `created_at` timestamp.
    *   By default, the list is sorted by `created_at` descending, so new ideas appear at the top.

2.  **Manual Reordering (Drag-and-Drop):**
    When a user drags an idea (`moved_idea`) to a new position:
    *   The `s` field of `moved_idea` will be calculated and stored (or updated if it already exists). No other idea documents are modified.
    *   Let `idea_above` be the idea immediately above the new target position, and `idea_below` be the idea immediately below the new target position.

    *   **Case 1: Moved between two existing ideas (`idea_above` and `idea_below` both exist):**
        *   `s_new = (effective_sort_value(idea_above) + effective_sort_value(idea_below)) / 2.0`
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 2: Moved to the absolute top of the list (`idea_above` does not exist):**
        *   Let `current_top_idea` be the idea that was previously at the top of the list (before this move).
        *   `s_new = effective_sort_value(current_top_idea) + 1.0`
        *   *(If the list was empty before moving this item, or if this is the only item being made top, `s_new` could default to its own `created_at + 1.0` or a predefined high value like `Date.now()`. The key is it must be higher than any other potential value. Using `previous_top_idea` is generally cleaner if the list wasn't empty).*
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 3: Moved to the absolute bottom of the list (`idea_below` does not exist):**
        *   Let `current_bottom_idea` be the idea that was previously at the bottom of the list.
        *   `s_new = effective_sort_value(current_bottom_idea) - 1.0`
        *   *(Similar to the top case, if the list was empty, `s_new` could be its `created_at - 1.0` or a predefined low value. The key is it must be lower.)*
        *   `moved_idea.s` is set to `s_new`.

**Rationale:**

*   **Minimized Firestore Writes:** Only the `s` field of the single moved idea needs to be written to Firestore. This is cost-effective and performant.
*   **Minimized Storage:** Ideas that have never been manually reordered do not consume extra storage for an `s` field.
*   **Leverages Default Timestamps:** The `created_at` field provides a natural and free default sort order.
*   **Handles Edge Cases:** The `****` and `-1.0` logic for top/bottom placement creates clear separation and ample "space" for future insertions without immediately needing to average very close numbers.
*   **Resilient to Concurrent Edits (Partially):** While not fully conflict-free without more complex logic, if two users reorder different items, their changes are isolated to those items' `s` values. Collisions occur if they move items relative to the *same* neighbors simultaneously.

**Considerations & Limitations:**

*   **Floating-Point Precision:** When repeatedly inserting items between two specific items using the averaging method, the difference between their `s` values shrinks. Double-precision floats offer ~50-52 such "halving" operations in the absolute worst-case scenario (always inserting into the same narrowing gap) before precision limits prevent further distinct ordering within that specific gap. This is highly unlikely to be hit in typical user behavior for an ideabook.
*   **Client-Side Sorting:** The sorting logic (calculating effective sort values and performing the sort) is handled on the client. This is efficient for lists up to ~1000 items. For significantly larger lists, server-side assistance or more complex querying might be needed.

---

**Example Scenario:**

Assume `created_at` timestamps are simplified integers for clarity. Higher value = newer.

**Initial State (No `s` values yet):**
1.  Idea A (created_at: 1000) -> Effective: 1000 (Top)
2.  Idea B (created_at: 900)  -> Effective: 900
3.  Idea C (created_at: 800)  -> Effective: 800
4.  Idea D (created_at: 700)  -> Effective: 700 (Bottom)

**Displayed Order:** A, B, C, D

**Action 1: User drags Idea D between Idea A and Idea B.**
*   `moved_idea` = D
*   `idea_above` = A (effective_sort_value: 1000)
*   `idea_below` = B (effective_sort_value: 900)
*   D's new `s` value = (1000 + 900) / 2.0 = **950.0**
*   Firestore: Update Idea D, set `D.s = 950.0`

**New State & Effective Values:**
1.  Idea A (created_at: 1000)         -> Effective: 1000
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea C (created_at: 800)          -> Effective: 800

**Displayed Order:** A, D, B, C

**Action 2: User drags Idea C to the top of the list.**
*   `moved_idea` = C
*   `idea_above` = null (moving to top)
*   `current_top_idea` = A (effective_sort_value: 1000)
*   C's new `s` value = 1000 + 1.0 = **1001.0**
*   Firestore: Update Idea C, set `C.s = 1001.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea A (created_at: 1000)         -> Effective: 1000
3.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
4.  Idea B (created_at: 900)          -> Effective: 900

**Displayed Order:** C, A, D, B

**Action 3: User drags Idea A to the bottom of the list.**
*   `moved_idea` = A
*   `idea_below` = null (moving to bottom)
*   `current_bottom_idea` = B (effective_sort_value: 900)
*   A's new `s` value = 900 - 1.0 = **899.0**
*   Firestore: Update Idea A, set `A.s = 899.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea A (created_at: 1000, s: 899.0) -> Effective: 899.0

**Displayed Order:** C, D, B, A

This refined description should provide a clear understanding of your sorting algorithm, its benefits, and how it works in practice.
```

# LLM

LLM is used to transcribe voice recording to text, refine and summarize the text, and provide chat functions.

The app may have multiple API backends such as Gemini, Gemma and Grok, etc to handle different use cases, e.g. Gemini for audio transcription, Gemma for chat.

Gemini API key is stored in `api_key_gemini.txt` file.
Grok API key is stored in `api_key_grok.txt` file.
Do not commit the API key files to the repository. Do not load and expect to read these files in the app. Hard code the keys in the code so that it's embedded in the app binary and is not visible to the user. Do not store the API key in user local device.

Use structured output JSON format for all LLM calls to make it easier to parse the output.
https://ai.google.dev/gemini-api/docs/structured-output?lang=rest

## Firebase remote config

Use Firebase remote config to store the LLM prompts. This way, we can change the prompts without releasing a new app version.

Read https://firebase.google.com/docs/remote-config/get-started?platform=flutter
carefully to understand how to use remote config.

Use real-time Remote Config to listen to remote config changes.
Read https://firebase.google.com/docs/remote-config/real-time?platform=flutter
carefully to understand how to listen to remote config changes.

Set `minimumFetchInterval` to 1 minute for debug mode for fast testing.

Refactor the current LLM prompts to be stored in Firebase remote config. Refactor the prompts to use JSON format for Firebase remote config parameters templates. I will upload the JSON template file to Firebase manually.

Use `parameterGroups` to group per user tier (free vs. paid).

Below is an example:

```json
{
  "parameterGroups": {
    "Free Tier Prompts": {
      "parameters": {
        "audio_transcription_prompt": {
          "defaultValue": {
            // Use {{}} for variables that will be filled in by the app.
            "value": "Audio transcription prompt here. {{user_ideas}}. other stuff...",
            "description": "This is the prompt to transcribe user voice audio to text.",
            "valueType": "STRING"
          }
        },
        // Add more prompts here...

        // Add model name and generation config here...
        "audio_transcription_model_name": {
          "defaultValue": {
            "value": "gemini-2-0-flash-lite.. use the current model value"
          },
          "valueType": "STRING"
        },
        // Other model names...
        "audio_transcription_generation_config": {
          "defaultValue": {
            // These are just example values. Use the real values...
            "value": "{\"temperature\": 0.9, \"maxOutputTokens\": 2048, \"topP\": 0.9, \"topK\": 20}"
          },
          "valueType": "JSON"
        },
      }
    },
    "Paid Tier Prompts": {
      // Paid tier prompts are the same as free tier for now.
    }
  }
}
```

Your task is to refactor the prompts to use the above JSON format. And create such JSON file.

And change the code to get the prompts, model name, and generation config from the Firebase remote config with the real-time Remote Config listen function provided by the Firebase SDK and the remote config package.

## Transcribe voice recording to text

Use a Google Gemini API to transcribe voice recording to text. The API provides the function to take audio input.

Use this model for this purpose: gemini-2.0-flash-lite

Call the REST API to transcribe voice recording. Use this doc as reference: https://ai.google.dev/gemini-api/docs/audio#rest
If the audio file is greater 15MB, use File API to upload the audio file and use it in prompt. If the audio file is less than 15MB, pass the audio data inline. The above referenced doc doesn't have inline audio data example for REST API; you can refer to this video doc as reference: https://ai.google.dev/gemini-api/docs/video-understanding#rest_1 (but need to change it to work with audio).

### New ideabook

When the user clicks the "New Ideabook" button in the Ideabooks list page, and finish recording the voice, the audio file is sent to Gemini API to transcribe the voice to text, based on the above instruction and pick the best color for the ideabook.

The prompt should be:
"""
Audio is the user's prompt to create an ideabook.

Your task is to:
1. transcribe the audio and summarize the transcription into a short name that best describes the ideabook.
2. Select a color that best represents the ideabook:
- `red`: work, professional, etc.
- `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
- `blue`: personal growth, health, time management, etc.
- `yellow`: inspiration, diary, daily log, journal, etc.
- `orange`: travel, planning, birthday, parties, wedding, etc.
- `purple`: everything else but try to much other colors if possible.

Output in the major language in user's prompt.

Output in JSON format:
```
{
  "short_name": "short name of the ideabook. <= 10 words. no repeat 'ideabook'",
  "color": "color of the ideabook. one of red, green, blue, yellow, orange, purple"
}
```
"""

Use this JSON output to create the ideabook with the given short_name and the selected color.

When waiting for the Gemini response, keep showing the waveform in its last state, turn the check mark button into a loading state (e.g. a spinner). Do not show notification like "Transcribing audio...". Only show notification once the Gemini response is done and new ideabooks is created and saved.

The notification should be displayed in place of the bottom panel where it shows the "new ideabook" button. the bottom panel turns into a "notification" mode to show the notification content. once the notification is gone after a short period of time, the bottom panel goes back to normal state and the "new ideabook" button shows up again. In the notification mode, the row shows only the notification message and a check mark icon to indicate success; don't show anything else.

### New idea

When the user clicks the microphone button in the ideabooks row in the ideabooks list page, and finish recording the voice, the audio file is sent to Gemini API to transcribe the voice to text and refine the text. The response from Gemini is used as the content of the idea.

The prompt to Gemini should be:
"""
You're an ideabook - a book collecting user's ideas. Your task is to help the user capture ideas.

Your name as an ideabook: <ideabook_short_name>

The attached audio is the voice recording of a user's idea. Your task is to:
* Transcribe the audio
* Polish the transcription by correcting grammar errors, mispoken words, removing unnecessary / duplicated words, etc.
* Add 1-2 emojis into the transcription if it brings fun and delight.
* Also summarize the transcription into a short title that best describes the idea. MUST be <= 5 words.

CRITICAL:
* You MUST respect and don't change the user's original meaning.
* You MUST NOT add any additional information that is not in the original audio.
* DO NOT disclose details of yourself even if user asks (e.g. model name, etc). You are just an ideabook.
* DO NOT change user's language in the transcription. If user's voice is in a mix of languages, keep the mix of languages in the transcription.

Output in JSON format:
{
  "short_title": "summarize the idea in <= 5 words.",
  "idea": "the full transcription of the idea."
}
"""

Use this JSON output to add the idea to the ideabook that the user clicks.

When waiting for the Gemini response, keep showing the waveform in its last state, turn the check mark button into a loading state (e.g. a spinner). Do not show notification like "Transcribing audio...". Only show notification once the Gemini response is done and new idea is saved.

The notification should be displayed inside the ideabook row. the row turns into a "notification" mode to show the notification content. once the notification is gone after a short period of time, the ideabook row goes back to normal state. In the notification mode, the row shows only the notification message and a check mark icon to indicate success; don't show the color indicator and anything else.

The notification message should show the short title of the idea: "Added <short_title>". The short title comes from LLM response `short_title` field in the JSON output.

### Chat voice input transcription

Gemini is used to trancribe the voice input in the chat box to text. The transcribed text is used as the user's prompt to send to LLM for chat.

When user finished recording, send the audio to Gemini for transcription. The prompt to Gemini should be:
"""
Transcribe audio to text. Refine text by correcting grammar errors, mispoken words, etc. Keep the original meaning.
Output in JSON format:
{
  "transcript": "transcribed and refined text."
}
"""

While waiting for Gemini's response, freeze the waveform, turns the checkmark button into a loading state (e.g. a spinner). Similar to the recording mode in the ideabooks list page. After got Gemini's response, do not show any notification; turns the chat box row back to normal state.

The transcribed text is then inserted to the end of the existing text in the chat box, with an empty space in between. Use can keep typing more texts into it or record more voices to be transcribed and appended to the chat box.

## Chat with your ideabook

Hard code the API key for Gemini API in the code. Do not store the API key in user local device.

Chat allows user to chat with LLM about the current selected ideabook. The current ideabook's name, and all the ideas are used as context input to the LLM conversation. Also include the previous chat history as context input. Render the LLM response in Markdown format.

Use the following prompt:
"""
You're an ideabook - a book collecting user's ideas. Your task is to understand user's intention and answer their prompts based on ideas captured so far.

Your name as an ideabook: <ideabook_short_name>
Ideas captured:
(format: idea creation date | idea content)
* <idea_date_only_no_time> | <idea_text>
* <idea_date_only_no_time> | <idea_text>
* <idea_date_only_no_time> | <idea_text>
...

User's prompt:
<user_prompt>

Previous chat history:
<past_25_chat_messages_from_both_user_and_LLM>

There are several guidelines you MUST follow no matter what the user asks:
* Respond in a friendly, engaging, and conversational style like talking to a close friend
* Do not disclose details of yourself even if user asks (e.g. model name, etc). You are just an ideabook.

Your reponse style:
* Prefer being brief and to the point unless it's more appropriate to be more detailed.
* Prefer responding based on the ideas captured but you can provide more information outside the ideabook if it makes the conversation more engaging.
* When responding with inforamtion outside the ideabook, friendly inform the user that and you may be wrong.
* Use emojis and emoticons in your response to make it more fun and engaging unless user asks to not to.
* Respond in user's language of choice. If user doesn't specify, guess based on the user input language (e.g. if user input is a mix of English and Chinese, respond in Chinese or English or mix as appropriate).

Also summarize the current prompt and all of user's previous prompts into a short summary prompt so that the summary prompt can be used as a new prompt for future conversation. Summarize from user's point of view, e.g. "I want to ..." instead of "The user wants to ...". Summarize in user's language (or mix of languages). IMPORTANT: The summary prompt should only include user's prompts, not ideas in the ideabook nor your response.

Output in JSON format:
{
  "user_prompt": "summary of user's prompts",
  "response": "your response to the user's prompt"
}
"""

Use Gemma (model name `gemma-3-27b-it`) API to provide chat functions.

## New chat prompt suggestions

When the chat history is empty (first time user chats with the ideabook or user cleared the chat history before), show a list of suggested prompts for user to try out.

The suggested prompts are dynamically generated by LLM based on the content of the ideabook. Use Gemma (model name `gemma-3-27b-it`) API to generate the suggested prompts.

The prompt to LLM should be:
"""
You're an ideabook - a book collecting user's ideas.

Your task is to understand user's goals with the ideabook and suggest 5 prompts for the most likely tasks (or questions) user might ask you about this ideabook.

Your name as an ideabook: <ideabook_short_name>
Ideas captured:
(format: idea creation date | idea content)
* <idea_date_only_no_time> | <idea_text>
* <idea_date_only_no_time> | <idea_text>
* <idea_date_only_no_time> | <idea_text>
...

Requirements for the suggested prompts:
* Order by the likelyhood user might click the prompt.
* The prompt should be a task or question the user might ask about the ideabook.
* The prompt MUST be 5 - 10 words.
* Use user's language (or mix of languages) used in the ideabook.

Here are some prompts you suggested before. You can still suuggest them again if they are still relevant. But consider suggesting new prompts that are more relevant to the ideabook.
* <suggested_prompt_1>
* <suggested_prompt_2>
* <suggested_prompt_3>
* <suggested_prompt_4>
* <suggested_prompt_5>

Output in JSON format:
{
  "prompts": [
    {"prompt": "suggested prompt #1"},
    {"prompt": "suggested prompt #2"},
    {"prompt": "suggested prompt #3"},
    {"prompt": "suggested prompt #4"},
    {"prompt": "suggested prompt #5"}
  ]
}
"""

Extract the suggested prompts from the JSON output.

While waiting for LLM response, show a brief message "Getting suggestions..." (with dot dot dot, dot dot dot, animating dots).

When LLM response is ready, show the suggested prompts. If user already started chatting, don't show the suggested prompts. Make sure the suggested prompts are only shown when the chat history is empty.

Make sure chatting with LLM can function correctly while still waiting for the suggested prompts. The LLM cloud function call should support some necessary parallelism.

The suggested prompts are clickable. Clicking a prompt will be like sending the chat message to LLM.

The suggested prompts are listed horizontally in a row above the chat box. Each suggested prompt is a box with the same style as the user message in the chat text area (e.g. background color, no outline, etc). Each suggested prompts should take exactly two lines vertically and takes up about 50% of the full width of the screen.

## Notes

Add a button "Save as Note" under every LLM response. clicking the button will save the LLM response (raw markdown) as a note. The new note should be displayed in the Notes tab.

### LLM failure retry

LLM response sometimes fail. Should retry at least one more time (after waiting a bit) before giving up. The retry happens automatically without user interaction.

# Theme

The app supports light and dark themes. The light/dark theme is remembered in the user preference. stored persistently. when app closes and restarts, the theme is restored.

The default theme is light when the user first opens the app.

# Cloud Integration

Use Firebase cloud integration for backend functions such as user authentication, data store/sync, etc.

## LLM APIs

LLM APIs (Gemini, Grok, etc.) should be hosted in Firebase Cloud Functions. Use the 2nd Gen Cloud Functions.
Refer to https://firebase.google.com/docs/functions/callable?hl=en&authuser=0&gen=2nd

CRITICAL:
* The function code must be implemented in `functions` folder with main entry point in `index.js`.
* The code must be implemented in Javascript.
* The code must use the Firebase callable interface (`onCall`).

The client app should call the firebase callable function instead of calling Gemini/Grok APIs directly.
Design firebase function interface to be generic so that it can be used for different LLM APIs.
The interface should include:
* Request:
  ** Which LLM API to call (Gemini or Grok)
  ** The request body to be sent to the LLM API
* Response:
  ** The output result from the LLM API call

Design the function interface so that, with this cloud function, the client app can call LLM API like they are calling the LLM directly from the client app with similar input parameters and output result interface.

The function implementation should call LLM APIs using REST API with Javascript.
The function simply passes through the request and response between the client app and the LLM API. It does not do any processing or transformation on the request or response.

The LLM API key can be hardcoded in the function code for now. We will migrate it later to secure storage.

# Authentication

The app does not require sign in to use. Signed-out user can still use the app freely. The user can choose the sign in by clicking the sign in option in the ideabooks list page menu.

User can authenticate with social sign in:
* Google, Facebook, Twitter, Apple, etc.

Use Firebase Authentication for user authentication.

Test authentication with Firebase Local Emulator Suite.
https://firebase.google.com/docs/auth/flutter/start?authuser=0&hl=en#optional_prototype_and_test_with_firebase_local_emulator_suite
`await FirebaseAuth.instance.useAuthEmulator('127.0.0.1', 9099);`
Make the initialization code easy to switch between production and emulator mode.

In the ideabooks list page, add the authentication option to the three-line menu. It should be the first option at the top.
1. when user is not signed in yet, show "[user icon] Sign in" button to direct user to sign in page.
2. when user is already signed in, show the user's profile picture and name. clicking it nagivates to the user profile page. The user profile page shows the user's name, email, and a "Sign out" button. (Will add more features later).

Create a sign in page. It should have a back button to go back to ideabooks list page.
The sign in page should list all the supported sign in options.
Clicking on the sign in option should launch the sign in flow.

Maintain an app wide global state to reflect the user's authentication state (the Firebase authentication `User` object). Get the current user's authentication state by listening to `FirebaseAuth.instance.idTokenChanges()`. The other parts of the app can read this global user state to determine if the user is signed in or not, and in the future, if the user is free or premium (by checking the user's subscription status via Firebase Custom Claims: https://firebase.google.com/docs/auth/admin/custom-claims#access_custom_claims_on_the_client) to control access to certain features behind paywall.

For Google sign in, use Firebase Google sign in.
https://firebase.google.com/docs/auth/flutter/federated-auth?hl=en&authuser=0#google
Install the official `google_sign_in` plugin.
Example code:
```
import 'package:google_sign_in/google_sign_in.dart';

Future<UserCredential> signInWithGoogle() async {
  // Trigger the authentication flow
  final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

  // Obtain the auth details from the request
  final GoogleSignInAuthentication? googleAuth = await googleUser?.authentication;

  // Create a new credential
  final credential = GoogleAuthProvider.credential(
    accessToken: googleAuth?.accessToken,
    idToken: googleAuth?.idToken,
  );

  // Once signed in, return the UserCredential
  return await FirebaseAuth.instance.signInWithCredential(credential);
}
```

## Welcome page

The app should show a welcome page when the user first opens the app. The main purpose of the welcome page is to ask user sign in with their social account before they can continue to use the app.

Logo: Voji in Pacifico font.
Slogan: "Ideas Spoken. Stories Born". in signika google font.
Sign in to Continue, in Afacad font.
Only 1 sign option: "Sign in with Google"
Sun / Moon icons toggle to pick theme. By default Sun is picked (light theme). When light theme is picked, show the moon icon to switch to dark theme. When dark mode is picked, show the sun icon to switch to light theme.

# Cloud data store

Use Google Cloud Firestore for cloud data store. Client data should be synced to /from cloud.

## Data model

collection(users):
  doc(userId):
    passcode | string: user's passcode (encrypted) to lock / unlock ideabooks
    collection(ideabooks):
      doc(ideabookId):
        name | string: ideabook name
        color | string: ideabook color
        isLocked | bool: ideabook is locked
        collection(ideas):
          doc(ideaId):
            content | string: idea content
            createdAt | time: idea created at
        collection(notes):
          doc(noteId):
            content | string: note content

Chat history is not synced to cloud. Chat history is stored locally only. Chat history is ok to be lost when user switch device or reinstall the app. User saves chat history as notes if they want to preserve it.

## Data fetch

Use onSnapshot to fetch initial data and listen to data changes and update data.
Refer to this doc: https://firebase.google.com/docs/firestore/query-data/listen

The ideabooks list page is always the first page when the app starts to render data.
onSnapshot listen on the /users/{userId}/ideabooks collection to get the initial and further updates to the ideabooks list. When user navigates away from the ideabooks list page, stop listening to the onSnapshot.

The {userId} is the Firebase Auth user ID which should be unique across users.

When user navigates to the ideabook detail page ideas list tab, start listening to the onSnapshot on the /users/{userId}/ideabooks/{ideabookId}/ideas collection, to get the initial and further updates to the ideas list. When user navigates away from the ideas tab or navigates away from the ideabook detail page, stop listening to the onSnapshot.

When user navigates to the ideabook detail page notes tab, start listening to the onSnapshot on the /users/{userId}/ideabooks/{ideabookId}/notes collection, to get the initial and further updates to the notes list. When user navigates away from the notes tab or navigates away from the ideabook detail page, stop listening to the onSnapshot.

The chat tab chat history is temporary and only stored locally.

## Data update

Use `add` to add new document to a collection and `set` to update a document. When adding a new document, let Firestore auto generate the document ID and store it in the document. When updating a document, use the document ID fetched from Firestore.

## Data deletion

When deleting an ideabook, first manually delete all the ideas and notes documents in the subcollections to make sure subcollections are deleted, then delete the ideabook document.

When deleting an idea or a note, directly delete the document.

## Flag

Introduce a flag to switch to use between firestore and local db. This is useful during development phase. We will later delete the local db function once we confirm firestore works well. But keep it for now and use a flag to switch between the two.


## Firestore optimization
now let's refactor the firestore related code to optimize firestore communication. 
the goal is to optimize the firestore interaction to minimize firestore cloud fee cost.
refer to https://cloud.google.com/firestore/pricing

firestore has the following cost:
* Document Reads
* Document Writes
* Document Deletes
* Storage

strategy:

### Document Writes
when creating new - call firestore to create only when the user input is a successul. e.g. if the the LLM transcription is failed, don't create the ideabook and / or idea document. after retry 3 times (with exponential backoff), if still failed, then don't create the document and just return error to user via in app notification.

when updating existing - save the original content (e.g. idea content, ideabook name), and compare with the new content before calling firestore to update. if the content is the same, don't call firestore to update. just exit the edit mode and don't even send notification to the user about update success since there is no change.

### Document Deletes
nothing to optimize here.

### Document Reads
CRITICAL: make sure Offline Persistence ENABLED. make the code change to explicitly enable it.

Do not use `get()` on a collection to fetch all documents. that will cost a lot because it forces to read all documents. instead, use `onSnapshot` to listen to the collection. that way, we only pay for the documents that are actually new / changed. when start to listen to `onSnapshot`, it will give you all the document on that collection. use that to initialize / update the UI.
CRITICAL: never use `get()` to get the entire collection! carefully check the code to remove such usage.

Because restarting the `onSnapshot` listener is expensive (considered as initial load of all documents), we should avoid to stop and restart listener too frequently.
Create a pool of listeners and make it a global state that can live across pages. Do not attach listeners to pages that can be disposed when user navigates away from the page.
When user opens the app or navigates to a page that needs to start a new listener on the collection for that page, i.e.:
- ideabooks collection for ideabooks list page
- ideas collection for ideas tab inside ideabook detail page
- notes collection for notes tab inside ideabook detail page
Create the listener and add it to the pool only if such listener is not already in the pool.
The listener should be keyed by the collection path. i.e.
- `ideabooks`
- `ideabooks/{ideabookId}/ideas`
- `ideabooks/{ideabookId}/notes`
On the other hand, we also don't want a listener to fetch updates forever in the background, e.g. user forgets to close the app. That will cost a lot for no better UX. 
So we will introduce its own TTL timer for each of the listener in the pool. At a high level, garbage collect the listeners that are not used for a long time.
After a period of time (e.g. 30 minutes) after the user last interacts with the page that uses that listener, we will stop the listener and remove it from the pool. When user comes back to the page, we will restart the listener if it has been stopped and removed from the pool.
If it's within the TTL, navigating to the page will reset the TTL to its original value (e.g. 30 minutes) for that listener.

In the ideabook detail page, there is no need to listen to the ideabook document change, e.g. the name of the ideabook changes. Can just keep using the original name while user is on the ideabook detail page. Only update it when the user navigates to the ideabooks list page.

### Storage
the ids should be as short as possible: document id, collection id. since we use embedded data hierarchy, there is no need to have long id. change to use timestamp for ids. use epoch timestamp in milli-second, and encode it (with A-Za-z0-9) to make it even shorter since id is a string.

field name takes up storage as well since firestore is schemaless. so use short field name. for example, rename the `isLocked` field to `l`. make all field names one letter to save space.

collection(users):
  doc(userId):
    p | string: user's passcode (encrypted) to lock / unlock ideabooks
    collection(ideabooks):
      doc(ideabookId):
        n | string: ideabook name
        c | single byte: ideabook color, encoded as single byte char.
        l | bool: ideabook is locked
        collection(ideas):
          doc(ideaId):
            c | string: idea content
        collection(notes):
          doc(noteId):
            c | string: note content

for color encoding, follow the following map:

Color   | Byte Value (Hex) | Byte Value (Decimal)
--------|------------------|--------------------
red     | 0x00             | 0
green   | 0x01             | 1
blue    | 0x02             | 2
yellow  | 0x03             | 3
orange  | 0x04             | 4
purple  | 0x05             | 5

firestore already has `created_at` and `updated_at` timestamp for every document automatically. no need to store them explicitly for any document. so remove such fields in all the documents and remove related code to set them. to read them, read from the system fields: 
DocumentSnapshot.createTime and DocumentSnapshot.updateTime.[4] These should also be Timestamp objects.

# Per user limits

The app has free tier and pro tier (paid user).

Free user:
* Limited to 3 ideabooks. 
* Limited to 10 ideas per ideabook.
* Limited to 10 notes per ideabook.
* Limited chat messages (including note refresh): 10 per day, 100 per month.
* Limited to 1 minute audio recording length.

Pro user:
* Limited to 1000 ideabooks.
* Limited to 100 ideas per ideabook.
* Limited to 100 notes per ideabook.
* Rate limit chat messages (including note refresh): 20 per minute, 1000 per day, 10000 per month.

Common limits:
* Limited to 100 total words per ideabook name.
* Limited to 1000 total words per idea.
* Limited to 200 total words per user input to chat message.
* Limited to 1000 total words per LLM response.
* Limited to 5 minutes audio recording length.

Old account clean up
* Account not used for 12 months will be automatically deleted.

## Client side enforcement

Client side enforcesment is cheap but can't prevent abuse. Client side enforces the above limits per user's tier.

When the user is over the limit, the app should show the upgrade page to let user upgrade from free tier to paid tier. For paid user over 1000 chat messages, friendly remind them to wait until next day (calcualte an exact time when they can chat again).

## Server side enforcement

Server side enforcement is more expensive but can't be bypassed. Server side enforces the above limits per user's tier.

## Firebase remote config

Use Firebase remote config to configure the limits. This way, we can change the limits without releasing a new app version.

Set the folloing parameters with in-app default parameter values:

for free users:
free_max_ideabooks (default: 3)
free_max_ideas_per_ideabook (default: 10)
free_max_notes_per_ideabook (default: 10)
free_chat_limit_daily (default: 10)
free_chat_limit_monthly (default: 100)
free_audio_recording_length_seconds (default: 60)
free_ideabook_name_max_words (default: 100)
free_idea_max_words (default: 1000)
free_chat_input_max_words (default: 200)
free_audio_recording_length_seconds (default: 300)

for pro users:
pro_max_ideabooks (default: 1000)
pro_max_ideas_per_ideabook (default: 100)
pro_max_notes_per_ideabook (default: 100)
pro_chat_rate_limit_minute (default: 20)
pro_chat_limit_daily (default: 1000)
pro_chat_limit_monthly (default: 10000)
pro_ideabook_name_max_words (default: 100)
pro_idea_max_words (default: 1000)
pro_chat_input_max_words (default: 200)
pro_audio_recording_length_seconds (default: 300)

your task is:
1. add the above parameters to the firebase remote config template json file `remote_config_voji-backend.json`. The `free_` prefixed parameters are under `Free Tier Limits` group. The `pro_` prefixed parameters are under `Paid Tier Limits` group. These are two new groups that should be added to the JSON file. I will upload the JSON file to Firebase manually.
2. set default values for local in-app default parameter values when setting up firebase remote config SDK.
3. change the code to read the limits from the firebase remote config.


# TOS

write the terms of services for the voji app. including privacy policy etc.

in the welcome page, a message to remind user they agree with the terms of service by signing in. the message is small font (google font acafad) and lighter color. The "terms of service" should be a clickable link. clicking that lead to a tos page. It should be dedicated page, not an overlay. it can be closed by clicking on x button.

# Onboarding

Key Concepts:
* Persistence: You need to ensure the tour only shows once per user (or until they complete it). shared_preferences is perfect for this.
* Targeting Widgets: You'll need a way to identify the specific widgets you want to highlight. GlobalKey is the standard Flutter mechanism for this.
* Overlay & Highlighting: Typically, the screen is dimmed, and the target widget is "cut out" or highlighted, often with an accompanying descriptive text or tooltip.

Use https://pub.dev/packages/showcaseview package to implement the tour function.

 Debugging: to make debugging easier, add a debug mode to the app. when debug mode is on, show the tour every time the user opens the app.

## Ideabooks list page

### Empty list
When the user for the first time ever to the ideabooks list page, show a tour to introduce the ideabooks list page.
The tour highlights two widgets:
1. The "New Ideabook" button. The tooltip says something like: click and say what your ideabook is about. AI will summarize into a short name. ideabook is where you keep your ideas organized.
2. The three-line menu button. The tooltip says something like: more options are in this menu. give it a try.

### First ideabook
When the user created the first ideabook, show another tour to introduce the ideabooks list.
The tour highlights several widgets in the first ideabook row:
1. The ideabook row. The tooltip says something like: this is your ideabook. click to see all the ideas in it, swipe left to show more options.
2. The color indicator. The tooltip says something like: click to change the color of the ideabook.
3. The microphone button. The tooltip says something like: click to capture new idea for this ideabook.

# Subscription

Use RevenueCat for subscription management.

Carefully read this document: https://www.revenuecat.com/docs/getting-started/quickstart#initialize-and-configure-the-sdk
To fully understand how to initialize and configure the RevenueCat SDK.
The SDK API Key is:
* For Apple platform: appl_KwMGXFBClCbKZTXwFFuFORGPGNW
* For Android platform: "to_be_created"
* Do not support other platforms yet.

The initialization and configuration should happen only after the user signs in (with Firebase authentication, right now only support Google as authentication provider).
Use the Firebase authentication user UID as the app user ID for RevenueCat.
Read this document very carefully to how the app user id should be provided.
https://www.revenuecat.com/docs/customers/identifying-customers#logging-in-during-configuration

Read this carefully:
https://www.revenuecat.com/docs/customers/identifying-customers#only-configure-the-sdk-with-a-custom-app-user-id
IMPORTANT: Only Configure the SDK with a custom App User ID so that Anonymous App User IDs are not created and used for purchase.

Do not Logout the User
Reference: https://www.revenuecat.com/docs/customers/identifying-customers#do-not-logout-the-user
After the user is logged in with a known App User ID, they may want to logout or switch accounts on your application (e.g., logout and then login with a different known ID). However, calling logout in the SDK will result in an anonymous App User ID being created. To resolve this, simply do not logout the SDK. In the case of switching accounts, you can call login when the user logs in to the different account with their new App User ID.

In the user profile page, add a text to show the app user ID and with a button to copy it. This will be useful for the user to contact support with their app user ID regarding their subscription.
Reference: https://www.revenuecat.com/docs/customers/identifying-customers#supporting-your-customers

now we've configured revenuecat for logged in user, let's add the function to show the revenuecat paywall.
read this very carefully to make sure you understand how to show paywall.
https://www.revenuecat.com/docs/tools/paywalls/displaying-paywalls#how-to-display-a-fullscreen-paywall-in-your-app-3
We want to show the fullscreen paywall.
Add a button in the user profile page to trigger showing the paywall.





# TODOs
* Set status bar style based on the theme.
* support text input for new ideabook and new idea.
* in the note detail page, the buttons should be smaller. 
make the text, icon and button size the same as the buttons below the LLM response in the chat page.

* keyboard should have the button to hide keyboard.
* pin note to the top
* when left a tab or page, any open text input should be closed / blurred.
* idea archieve.
* I18n version. App UI language and LLM chat language.

