/// App-wide constants
///
/// DEPRECATED: Most limits are now managed via Firebase Remote Config
/// Use AppLimitsService and related providers instead for user-tier specific limits
/// These constants are kept for backward compatibility and non-user-specific values

/// Maximum number of ideas per ideabook
/// DEPRECATED: Use maxIdeasPerIdeabookProvider instead
@Deprecated('Use maxIdeasPerIdeabookProvider for user-tier specific limits')
const int kMaxIdeasPerIdeabook = 100;

/// Maximum number of notes per ideabook
/// DEPRECATED: Use maxNotesPerIdeabookProvider instead
@Deprecated('Use maxNotesPerIdeabookProvider for user-tier specific limits')
const int kMaxNotesPerIdeabook = 100;

/// Maximum number of ideabooks per user
/// DEPRECATED: Use maxIdeabooksProvider instead
@Deprecated('Use maxIdeabooksProvider for user-tier specific limits')
const int kMaxIdeabooksPerUser = 1000;

/// Maximum length for ideabook name
/// DEPRECATED: Use ideabookNameMaxWordsProvider instead
@Deprecated('Use ideabookNameMaxWordsProvider for user-tier specific limits')
const int kMaxIdeabookNameLength = 100;

/// Maximum length for idea content
/// DEPRECATED: Use ideaMaxWordsProvider instead
@Deprecated('Use ideaMaxWordsProvider for user-tier specific limits')
const int kMaxIdeaContentLength = 1000;

/// Maximum duration for audio recording in seconds
/// DEPRECATED: Use audioRecordingLengthSecondsProvider instead
@Deprecated('Use audioRecordingLengthSecondsProvider for user-tier specific limits')
const int kMaxAudioRecordingDurationSeconds = 300;

/// Duration in seconds for the countdown warning before recording stops
const int kRecordingCountdownWarningSeconds = 10;
