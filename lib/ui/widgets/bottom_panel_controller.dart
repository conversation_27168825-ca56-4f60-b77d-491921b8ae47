import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:voji/models/models.dart' hide BottomPanelNotification;
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/services/limits/app_limits_providers.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/ui/providers/bottom_panel_notification_provider.dart';
import 'package:voji/ui/providers/first_ideabook_created_provider.dart';
import 'package:voji/ui/providers/ideabook_provider.dart';
import 'package:voji/ui/providers/recording_mode_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:voji/ui/widgets/showcase_tour.dart';
import 'package:voji/utils/color_utils.dart';
import 'package:voji/utils/logger.dart';

/// Enum representing the state of the bottom panel
enum BottomPanelState {
  /// Normal state showing the "New Ideabook" button
  normal,

  /// Recording state showing the audio recording panel
  recording,

  /// Notification state showing a notification message
  notification,
}

/// Provider to track the state of the bottom panel
final bottomPanelStateProvider = StateProvider<BottomPanelState>((ref) => BottomPanelState.normal);

/// Widget that controls the bottom panel of the ideabooks list screen
class BottomPanelController extends ConsumerStatefulWidget {
  const BottomPanelController({super.key});

  @override
  ConsumerState<BottomPanelController> createState() => _BottomPanelControllerState();
}

class _BottomPanelControllerState extends ConsumerState<BottomPanelController> {
  // Cached max ideabooks limit
  int? _maxIdeabooks;

  @override
  void initState() {
    super.initState();
    // Initialize the max ideabooks limit
    _initializeMaxIdeabooks();
  }

  /// Initialize the maximum ideabooks limit from the provider
  Future<void> _initializeMaxIdeabooks() async {
    try {
      _maxIdeabooks = await ref.read(maxIdeabooksProvider.future);
      Logger.debug('BottomPanelController: Max ideabooks initialized: $_maxIdeabooks');
    } catch (e) {
      Logger.error('BottomPanelController: Failed to get max ideabooks, using fallback', e);
      _maxIdeabooks = 1000; // Fallback to a reasonable limit
    }
  }

  @override
  Widget build(BuildContext context) {
    final panelState = ref.watch(bottomPanelStateProvider);
    final notification = ref.watch(bottomPanelNotificationProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _buildPanel(context, panelState, notification),
    );
  }

  /// Build the appropriate panel based on the current state
  Widget _buildPanel(BuildContext context, BottomPanelState state, BottomPanelNotification? notification) {
    switch (state) {
      case BottomPanelState.normal:
        return _buildNewIdeabookButton(context);
      case BottomPanelState.recording:
        return _buildRecordingPanel(context);
      case BottomPanelState.notification:
        if (notification != null) {
          return _buildNotificationPanel(context, notification);
        } else {
          // Fallback if notification is null but we're in notification state
          return _buildNewIdeabookButton(context);
        }
    }
  }

  // Fixed height for the bottom panel to maintain consistency across all states
  static const double kBottomPanelHeight = 80.0;

  /// Build the "New Ideabook" button
  Widget _buildNewIdeabookButton(BuildContext context) {
    return SizedBox(
      key: const ValueKey('new-ideabook-button'),
      height: kBottomPanelHeight,
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 200),
          child: Showcase(
            key: ShowcaseKeys.newIdeabookButton,
            description: 'Ideabook 📚 is where you keep your ideas ✨ organized.\n\nTap, speak your mind 🎙️ about your first ideabook, and Voji will auto-magically give it a name 😉',
            targetShapeBorder: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero, // Changed from circular(8) to zero for square corners
              side: BorderSide(color: VojiTheme.colorsOf(context).border, width: 2),
            ),
            tooltipBackgroundColor: VojiTheme.colorsOf(context).tooltipBackground,
            textColor: VojiTheme.colorsOf(context).textPrimary,
            tooltipPadding: const EdgeInsets.all(24.0),
            descTextStyle: GoogleFonts.afacad(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: VojiTheme.colorsOf(context).textPrimary,
            ),
            child: OutlinedButton(
              onPressed: () async {
                // First check if the user has reached the maximum number of ideabooks
                final ideabookRepository = ref.read(ideabookRepositoryProvider);
                final isAtLimit = await ideabookRepository.isUserAtIdeabookLimit();

                if (isAtLimit) {
                  // Show error dialog if the user has reached the maximum number of ideabooks
                  // Use a post-frame callback to show the dialog after the current frame is complete
                  if (mounted) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        showDialog(
                          context: context,
                          builder: (dialogContext) => AlertDialog(
                            title: Text(
                              'Maximum Ideabooks Reached! 🈵',
                              style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
                            ),
                            content: Text(
                              "Wow! You've hit the ${_maxIdeabooks ?? 1000} Ideabook milestone! 🏆 Amazing! To add more, you can free up space by deleting some you no longer need. 👍",
                              style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.zero,
                              side: BorderSide(
                                color: VojiTheme.colorsOf(dialogContext).border,
                                width: 1,
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(dialogContext).pop(),
                                child: Text(
                                  'OK',
                                  style: VojiTheme.textStylesOf(dialogContext).buttonText,
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                    });
                  }
                  return;
                }

                // Request microphone permission before entering recording mode
                final recordingService = ref.read(audioRecordingServiceProvider);

                // Check if permission is already granted
                final hasPermission = await recordingService.checkPermission();

                if (hasPermission) {
                  // Permission already granted, enter recording mode
                  ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.recording;
                  ref.read(recordingModeProvider.notifier).state = true;
                  return;
                }

                // For all platforms, directly request the permission
                // This ensures the system dialog appears on first request
                Logger.debug('Directly requesting microphone permission');
                final status = await recordingService.requestPermission();
                Logger.debug('Permission request result: ${status.name}');

                if (status.isGranted) {
                  // Permission granted, enter recording mode
                  ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.recording;
                  ref.read(recordingModeProvider.notifier).state = true;
                  return;
                }

                // If permission is denied, show settings dialog
                if (context.mounted) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Microphone Permission Required'),
                      content: const Text(
                        'Microphone permission is required to record audio. '
                        'Please enable it in app settings.'
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            openAppSettings();
                          },
                          child: const Text('Open Settings'),
                        ),
                      ],
                    ),
                  );
                }
              },
              style: Theme.of(context).outlinedButtonTheme.style,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.mic,
                    size: 24,
                    color: VojiTheme.colorsOf(context).textPrimary,
                  ),
                  const SizedBox(width: 8),
                  Text('New Ideabook', style: VojiTheme.textStylesOf(context).buttonText),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build the notification panel
  Widget _buildNotificationPanel(BuildContext context, BottomPanelNotification notification) {
    return SizedBox(
      key: const ValueKey('notification-panel'),
      height: kBottomPanelHeight,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Notification message
            Expanded(
              child: Text(
                notification.message,
                style: VojiTheme.textStylesOf(context).bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Check icon to indicate success
            Icon(
              Icons.check_circle,
              color: VojiTheme.getIdeabookColor(context, IdeabookColor.green.index),
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  /// Build the recording panel
  Widget _buildRecordingPanel(BuildContext context) {
    return SizedBox(
      height: kBottomPanelHeight,
      child: AdvancedAudioRecordingPanel(
        key: const ValueKey('recording-panel'),
        layout: AudioRecordingPanelLayout.horizontal,
        showTimer: false,
        onRecordingCompleted: _handleRecordingCompleted,
        onRecordingFailed: _handleRecordingFailed,
        onRecordingCancelled: _handleRecordingCancelled,
        onPermissionDenied: _handlePermissionDenied,
      ),
    );
  }

  /// Handle recording completion
  Future<void> _handleRecordingCompleted(String filePath, Duration duration) async {
    try {
      // Check if this is the first ideabook by counting existing ideabooks
      final ideabooksAsync = ref.read(ideabooksNotifierProvider);
      Logger.debug('Checking if this will be the first ideabook. Current ideabooks state: ${ideabooksAsync.toString()}');
      final willBeFirstIdeabook = ideabooksAsync.hasValue && ideabooksAsync.value!.isEmpty;
      Logger.debug('Will this be the first ideabook? $willBeFirstIdeabook (hasValue: ${ideabooksAsync.hasValue}, isEmpty: ${ideabooksAsync.hasValue ? ideabooksAsync.value!.isEmpty : "N/A"})');

      // Use the LLM service to transcribe the audio for new ideabook
      final llmService = ref.read(llmServiceProvider);
      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdeabook,
      );

      if (transcriptionResult.isSuccess &&
          transcriptionResult.shortName != null) {
        // Convert the color string to IdeabookColor enum
        final ideabookColor = ColorUtils.stringToIdeabookColor(
          transcriptionResult.color
        );

        if (transcriptionResult.color != null) {
          Logger.debug('Using LLM-selected color: ${ideabookColor.name}');
        }

        // Create a new ideabook with the transcription
        await ref.read(ideabooksNotifierProvider.notifier).createIdeabook(
          name: transcriptionResult.shortName!,
          color: ideabookColor,
          onCreated: () {
            // Set the flag to trigger scrolling to the bottom
            ref.read(newIdeabookCreatedProvider.notifier).state = true;

            // If this is the first ideabook, trigger the first ideabook tour
            if (willBeFirstIdeabook) {
              Logger.debug('Triggering first ideabook tour - setting firstIdeabookCreatedProvider to true');
              ref.read(firstIdeabookCreatedProvider.notifier).state = true;
              // Add a debug check to verify the state was set
              final wasSet = ref.read(firstIdeabookCreatedProvider);
              Logger.debug('firstIdeabookCreatedProvider state after setting: $wasSet');
            }
          },
        );

        // Show notification
        if (mounted) {
          // Switch to notification state
          ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;

          // Show notification
          ref.read(showBottomPanelNotificationProvider)(
            'Ideabook created successfully',
            duration: const Duration(seconds: 1, milliseconds: 500),
          );
        }
      } else {
        // Transcription failed, create with default values
        Logger.error('Transcription failed: ${transcriptionResult.errorMessage}');
        await ref.read(ideabooksNotifierProvider.notifier).createIdeabook(
          name: 'Voice Recording',
          color: IdeabookColor.none,
          onCreated: () {
            // Set the flag to trigger scrolling to the bottom
            ref.read(newIdeabookCreatedProvider.notifier).state = true;

            // If this is the first ideabook, trigger the first ideabook tour
            if (willBeFirstIdeabook) {
              Logger.debug('Triggering first ideabook tour - setting firstIdeabookCreatedProvider to true');
              ref.read(firstIdeabookCreatedProvider.notifier).state = true;
              // Add a debug check to verify the state was set
              final wasSet = ref.read(firstIdeabookCreatedProvider);
              Logger.debug('firstIdeabookCreatedProvider state after setting: $wasSet');
            }
          },
        );

        // Show notification for error
        if (mounted) {
          // Switch to notification state
          ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;

          // Show notification
          ref.read(showBottomPanelNotificationProvider)(
            'Transcription failed',
            duration: const Duration(seconds: 1, milliseconds: 500),
          );
        }
      }
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('Error processing recording', e);

      // Check if the error is due to reaching the maximum number of ideabooks
      if (e.toString().contains('Maximum number of ideabooks reached')) {
        // Switch back to normal state
        ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.normal;
        ref.read(recordingModeProvider.notifier).state = false;

        // Show error dialog if the user has reached the maximum number of ideabooks
        // Use a post-frame callback to show the dialog after the current frame is complete
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              showDialog(
                context: context,
                builder: (dialogContext) => AlertDialog(
                  title: Text(
                    'Maximum Ideabooks Reached! 🈵',
                    style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
                  ),
                  content: Text(
                    "Wow! You've created ${_maxIdeabooks ?? 1000} ideabooks! 🤯 To add more, please delete some existing ideabooks first.",
                    style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                    side: BorderSide(
                      color: VojiTheme.colorsOf(dialogContext).border,
                      width: 1,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(),
                      child: Text(
                        'OK',
                        style: VojiTheme.textStylesOf(dialogContext).buttonText,
                      ),
                    ),
                  ],
                ),
              );
            }
          });
        }
        return;
      }

      // For other errors, proceed with default error handling
      // Check if this is the first ideabook by counting existing ideabooks
      final ideabooksAsync = ref.read(ideabooksNotifierProvider);
      Logger.debug('Checking if this will be the first ideabook. Current ideabooks state: ${ideabooksAsync.toString()}');
      final willBeFirstIdeabook = ideabooksAsync.hasValue && ideabooksAsync.value!.isEmpty;
      Logger.debug('Will this be the first ideabook? $willBeFirstIdeabook (hasValue: ${ideabooksAsync.hasValue}, isEmpty: ${ideabooksAsync.hasValue ? ideabooksAsync.value!.isEmpty : "N/A"})');

      // Create ideabook with error message
      try {
        await ref.read(ideabooksNotifierProvider.notifier).createIdeabook(
          name: 'Voice Recording',
          color: IdeabookColor.none,
          onCreated: () {
            // Set the flag to trigger scrolling to the bottom
            ref.read(newIdeabookCreatedProvider.notifier).state = true;

            // If this is the first ideabook, trigger the first ideabook tour
            if (willBeFirstIdeabook) {
              Logger.debug('Triggering first ideabook tour - setting firstIdeabookCreatedProvider to true');
              ref.read(firstIdeabookCreatedProvider.notifier).state = true;
              // Add a debug check to verify the state was set
              final wasSet = ref.read(firstIdeabookCreatedProvider);
              Logger.debug('firstIdeabookCreatedProvider state after setting: $wasSet');
            }
          },
        );
      } catch (createError) {
        // If we can't create the ideabook (e.g., due to limit), just show an error notification
        Logger.error('Failed to create fallback ideabook', createError);
      }

      // Show notification for error
      if (mounted) {
        // Switch to notification state
        ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;

        // Show notification
        ref.read(showBottomPanelNotificationProvider)(
          'Error processing recording',
          duration: const Duration(seconds: 1, milliseconds: 500),
        );
      }
    }
  }

  /// Handle recording failure
  void _handleRecordingFailed(String errorMessage) {
    // Switch to notification state
    ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;

    // Show notification for error
    ref.read(showBottomPanelNotificationProvider)(
      'Recording failed',
      duration: const Duration(seconds: 1, milliseconds: 500),
    );
  }

  /// Handle recording cancellation
  void _handleRecordingCancelled() {
    // Just go back to normal state
    ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.normal;
    ref.read(recordingModeProvider.notifier).state = false;
  }

  /// Handle permission denial
  void _handlePermissionDenied() {
    // Switch to notification state
    ref.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;

    // Show notification for permission error
    ref.read(showBottomPanelNotificationProvider)(
      'Microphone permission required',
      duration: const Duration(seconds: 1, milliseconds: 500),
    );

    // Show permission dialog after notification is dismissed
    Future.delayed(const Duration(milliseconds: 1600), () {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Microphone Permission Required'),
            content: const Text(
              'Microphone permission is required to record audio. '
              'Please enable it in app settings.'
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('Open Settings'),
              ),
            ],
          ),
        );
      }
    });
  }
}
