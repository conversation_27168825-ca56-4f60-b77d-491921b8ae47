import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/audio/note_title_recording_controller.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/remote_config/remote_config_providers.dart';
import 'package:voji/services/tour/tour_service.dart';

import 'package:voji/ui/providers/ideabook_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/common/reusable_audio_recording_panel.dart';
import 'package:voji/ui/widgets/showcase_tour.dart';
import 'package:voji/utils/id_utils.dart';
import 'package:voji/utils/logger.dart';

/// Screen for displaying a note's details in a full page
class NoteDetailScreen extends ConsumerStatefulWidget {
  /// The note to display
  final Note note;

  /// The ideabook ID this note belongs to
  final String ideabookId;

  /// Constructor
  const NoteDetailScreen({
    super.key,
    required this.note,
    required this.ideabookId,
  });

  @override
  ConsumerState<NoteDetailScreen> createState() => _NoteDetailScreenState();
}

class _NoteDetailScreenState extends ConsumerState<NoteDetailScreen>
    with SingleTickerProviderStateMixin {
  /// Text editing controller for the note title
  late TextEditingController _textEditingController;

  /// Focus node for the text field
  final _focusNode = FocusNode();

  /// Whether the note title is in edit mode
  bool _isEditingTitle = false;

  /// Whether the note title is in recording mode
  bool _isRecordingTitle = false;

  /// Whether the note is being regenerated
  bool _isRegenerating = false;

  /// Animation controller for the thinking dots
  late AnimationController _dotsController;

  /// Current dot count for the thinking animation
  int _dotCount = 1;

  /// Current note content - used to update UI without waiting for Firestore
  late String _currentNoteContent;

  /// Current note title - used to update UI without waiting for Firestore
  late String _currentNoteTitle;

  /// Flag to track if the tour has been initialized
  bool _tourInitialized = false;

  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController(text: widget.note.title);
    _currentNoteContent = widget.note.content;
    _currentNoteTitle = widget.note.title;

    // Initialize animation controller for thinking dots
    _dotsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          // Cycle through 1, 2, 3 dots
          _dotCount = (_dotCount % 3) + 1;
        });
        _dotsController.reset();
        _dotsController.forward();
      }
    });

    // Check if we should show the note detail tour
    // Delay to ensure the UI is fully rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowTour();
    });
  }

  /// Check if the tour should be shown and show it if needed
  Future<void> _checkAndShowTour() async {
    if (_tourInitialized) {
      Logger.debug('Note detail tour already initialized, skipping');
      return;
    }

    try {
      // Use the TourService to show the note detail tour
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (mounted) {
          final shown = await TourService.showNoteDetailTour(context);
          Logger.debug('Note detail tour shown: $shown');
        }
      });

      _tourInitialized = true;
      Logger.debug('Note detail tour initialization completed');
    } catch (e) {
      Logger.error('Error initializing note detail tour', e);
    }
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _focusNode.dispose();
    _dotsController.dispose();

    // Cancel recording if still active
    if (_isRecordingTitle && _recordingController != null) {
      _recordingController!.cancelRecording(ref);
    }

    super.dispose();
  }

  /// Save changes to the note title
  Future<void> _saveChanges() async {
    // Get the trimmed text
    final newTitle = _textEditingController.text.trim();

    Logger.debug(
      'Saving note title changes: current="$_currentNoteTitle", new="$newTitle"',
    );

    // Only save changes if the content has actually changed
    if (newTitle.isNotEmpty && newTitle != _currentNoteTitle) {
      try {
        // Create a new note with the updated title
        final updatedNote = widget.note.copyWith(
          title: newTitle,
          updatedAt: DateTime.now(),
        );

        // Update the note in Firestore
        Logger.debug(
          'Updating note in Firestore: id=${updatedNote.id}, title="${updatedNote.title}"',
        );
        final firestoreService = ref.read(firestoreServiceProvider);
        final success = await firestoreService.updateNote(widget.ideabookId, updatedNote);
        Logger.debug('Firestore update result: $success');

        if (success) {
          if (mounted) {
            // Update the local state with the new title
            setState(() {
              _currentNoteTitle = newTitle;
              _isEditingTitle = false;
              _isRecordingTitle = false;
              _recordingController = null;
            });

            // No notification needed after saving title changes
            // Just log the change
            Logger.debug('Note title updated, regenerating content...');

            // Automatically trigger regeneration of the note content
            if (mounted) {
              Logger.debug(
                'Triggering automatic note regeneration after title update',
              );
              // Use Future.microtask to ensure the UI updates before starting regeneration
              Future.microtask(() => _regenerateNote(isAutomatic: true));
            }
          }
        } else {
          if (mounted) {
            // Log the failure
            Logger.debug('Failed to update note title in Firestore');

            // Exit edit mode
            setState(() {
              _isEditingTitle = false;
              _isRecordingTitle = false;
              _recordingController = null;
            });
          }
        }
      } catch (e) {
        if (mounted) {
          // Log the error but don't show a notification
          Logger.error('Error updating note title', e);

          // Exit edit mode
          setState(() {
            _isEditingTitle = false;
            _isRecordingTitle = false;
            _recordingController = null;
          });
        }
      }
    } else {
      // No changes or empty title, just exit edit mode
      setState(() {
        _isEditingTitle = false;
        _isRecordingTitle = false;
        _recordingController = null;
      });
    }
  }

  /// Delete the note with confirmation dialog
  Future<void> _deleteNote() async {
    // Store local references before any async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final errorColor = VojiTheme.colorsOf(context).error;

    // Get appropriate text styles for regular and error messages
    final snackBarTextColor = VojiTheme.getSnackBarTextColor(context);
    final errorSnackBarTextColor = VojiTheme.getSnackBarTextColor(
      context,
      backgroundColor: errorColor,
    );

    // Show confirmation dialog
    final bool? confirmDelete = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Delete Note',
            style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to delete this note?',
            style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: VojiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: VojiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Delete',
                style: VojiTheme.textStylesOf(dialogContext).buttonText
                    .copyWith(color: VojiTheme.colorsOf(dialogContext).error),
              ),
            ),
          ],
        );
      },
    );

    // Check if the widget is still mounted after the dialog
    if (!mounted) return;

    // If user confirmed deletion
    if (confirmDelete == true) {
      try {
        Logger.debug('Deleting note with ID: ${widget.note.id}');

        // Delete the note using Firestore service directly
        final firestoreService = ref.read(firestoreServiceProvider);
        final success = await firestoreService.deleteNote(widget.ideabookId, widget.note.id);

        if (success) {
          // Show notification
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'Note deleted',
                style: TextStyle(color: snackBarTextColor),
              ),
              duration: const Duration(seconds: 1),
            ),
          );

          // Navigate back to the notes list
          if (mounted) {
            Navigator.of(context).pop();
          }
        } else {
          // Show error notification
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'Failed to delete note',
                style: TextStyle(color: snackBarTextColor),
              ),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        Logger.error('Error deleting note', e);

        // Show error notification
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Error deleting note: $e',
              style: TextStyle(color: errorSnackBarTextColor),
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: errorColor,
          ),
        );
      }
    }
  }

  /// Handle voice recording completion
  void _handleTranscriptionComplete(String transcribedText) {
    if (!mounted) {
      Logger.debug(
        '_handleTranscriptionComplete called but widget is not mounted',
      );
      return;
    }

    // Get the current text and cursor position
    final currentText = _textEditingController.text;
    final selection = _textEditingController.selection;

    // Determine where to insert the transcribed text
    String newText;
    int newCursorPosition;

    if (selection.isValid) {
      // Insert at cursor position
      final beforeCursor = currentText.substring(0, selection.start);
      final afterCursor = currentText.substring(selection.end);

      // Create the new text with the transcription inserted at cursor (no space added)
      newText = beforeCursor + transcribedText + afterCursor;

      // Calculate new cursor position (after the inserted text)
      newCursorPosition = beforeCursor.length + transcribedText.length;
    } else {
      // If no valid selection, append to the end (no space added)
      newText =
          currentText.isEmpty ? transcribedText : currentText + transcribedText;
      newCursorPosition = newText.length;
    }

    // Update the text field
    _textEditingController.text = newText;

    // Set cursor position after the inserted text
    _textEditingController.selection = TextSelection.fromPosition(
      TextPosition(offset: newCursorPosition),
    );

    // Exit recording mode and clear the controller reference
    setState(() {
      _isRecordingTitle = false;
      _recordingController = null;
    });

    // Re-focus the text field to allow immediate editing
    _focusNode.requestFocus();

    // Log the action
    Logger.debug(
      'Transcribed text inserted at cursor position. User can now edit and save manually.',
    );
  }

  /// Recording controller for note title
  NoteTitleRecordingController? _recordingController;

  /// Start voice recording
  void _startRecording() {
    // Cancel any existing recording first
    if (_recordingController != null) {
      _recordingController!.cancelRecording(ref);
      _recordingController = null;
    }

    setState(() {
      _isRecordingTitle = true;
    });

    // Create a recording controller and store it for reuse
    _recordingController = NoteTitleRecordingController(
      noteId: widget.note.id,
      onTranscriptionComplete: _handleTranscriptionComplete,
    );

    // Start recording
    _recordingController!.startRecording(ref);
  }

  /// Build the title edit mode UI
  Widget _buildTitleEditMode() {
    if (_isRecordingTitle) {
      // Show recording UI
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Text field for editing the title - keep enabled to preserve cursor position
          TextField(
            controller: _textEditingController,
            focusNode: _focusNode,
            style: VojiTheme.textStylesOf(context).bodyMedium,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              isDense: true,
            ),
            maxLines: null, // Allow multiple lines
            enabled: true, // Keep enabled to preserve cursor position
          ),

          const SizedBox(height: 16),

          // Recording panel - use the existing controller instance with a key to prevent recreation
          ReusableAudioRecordingPanel(
            key: ValueKey(_recordingController),
            controller: _recordingController!,
            config: AudioRecordingPanelConfig(
              layout: AudioRecordingPanelLayout.horizontal,
              showTimer: false,
              color: VojiTheme.colorsOf(context).textPrimary,
              // Disable auto-start since we already started recording
              autoStart: false,
            ),
            // Add onCancel callback to properly handle cancellation in the parent widget
            onCancel: () {
              Logger.debug('Note title recording cancelled via X button');
              // Exit recording mode and clear the controller reference
              setState(() {
                _isRecordingTitle = false;
                _recordingController = null;
              });
            },
          ),
        ],
      );
    } else {
      // Show edit UI
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Text field for editing the title
          TextField(
            controller: _textEditingController,
            focusNode: _focusNode,
            style: VojiTheme.textStylesOf(context).bodyMedium,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              isDense: true,
            ),
            maxLines: null, // Allow multiple lines
          ),

          const SizedBox(height: 16),

          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side - Mic button
              IconButton(
                icon: Icon(
                  Icons.mic,
                  color: VojiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: _startRecording,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Right side - Cancel and Save buttons
              Row(
                children: [
                  // Cancel button
                  TextButton(
                    onPressed: () {
                      // Exit edit mode without saving
                      setState(() {
                        _isEditingTitle = false;
                        _isRecordingTitle = false;
                        _recordingController = null;
                      });
                    },
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: VojiTheme.colorsOf(context).textPrimary,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Save button
                  TextButton(
                    onPressed: _saveChanges,
                    child: Text(
                      'Save',
                      style: TextStyle(
                        color: VojiTheme.colorsOf(context).textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the ideabook to determine the color
    final ideabookAsync = ref.watch(ideabookProvider(widget.ideabookId));

    return ideabookAsync.when(
      loading: () => _buildLoadingScaffold(context),
      error: (error, _) => _buildErrorScaffold(context, error),
      data: (ideabook) {
        if (ideabook == null) {
          return _buildNotFoundScaffold(context);
        }

        // Get the ideabook color and appropriate text color
        final ideabookColor = VojiTheme.getIdeabookColor(
          context,
          ideabook.color.index,
        );
        final textColor = VojiTheme.getTextColorForIdeabookColor(
          context,
          ideabook.color.index,
        );

        return Scaffold(
          appBar: AppBar(
            // Custom title widget with explicit layout
            title: null, // Remove default title
            automaticallyImplyLeading:
                false, // Don't automatically add back button
            backgroundColor: ideabookColor,
            flexibleSpace: SafeArea(
              child: Row(
                children: [
                  // Back button (left-aligned)
                  IconButton(
                    icon: Icon(Icons.arrow_back, color: textColor),
                    onPressed: () => Navigator.of(context).pop(),
                  ),

                  // Title (right-aligned)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        right: 16.0,
                      ), // Add right padding
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          ideabook.name,
                          style: GoogleFonts.afacad(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: textColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            elevation: 0,
          ),
          body: SafeArea(
            // Use a Column to ensure the content takes up the full available space
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Expanded ensures the scroll view takes all available space
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.fromLTRB(16.0, 20.0, 16.0, 0.0),
                    // Use BouncingScrollPhysics to allow bouncing even when content doesn't fill the screen
                    physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics(),
                    ),
                    // ClampingBehavior.none ensures it always bounces back to original position
                    clipBehavior: Clip.none,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Note title (user prompt) with gray background taking full width
                        GestureDetector(
                          onTap: () {
                            // Enter edit mode when tapping on the title, but only if not regenerating
                            if (!_isEditingTitle &&
                                !_isRecordingTitle &&
                                !_isRegenerating) {
                              setState(() {
                                _isEditingTitle = true;
                                // Reset text controller to current note title
                                _textEditingController.text = _currentNoteTitle;
                              });
                              // Focus the text field
                              _focusNode.requestFocus();
                            }
                          },
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(
                              0.0,
                              0.0,
                              0.0,
                              20.0,
                            ),
                            padding: const EdgeInsets.all(16.0),
                            decoration: BoxDecoration(
                              color:
                                  VojiTheme.colorsOf(
                                    context,
                                  ).chatMessageBackground,
                              border: null,
                            ),
                            child:
                                _isEditingTitle
                                    ? _buildTitleEditMode()
                                    : Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Chat icon
                                        Icon(
                                          Icons.chat_bubble_outline,
                                          size: 24.0,
                                          color:
                                              VojiTheme.colorsOf(
                                                context,
                                              ).textPrimary,
                                        ),

                                        // Space between icon and text
                                        const SizedBox(width: 16.0),

                                        // Note title with expanded width
                                        Expanded(
                                          child: Text(
                                            _currentNoteTitle,
                                            style:
                                                VojiTheme.textStylesOf(
                                                  context,
                                                ).bodyMedium,
                                          ),
                                        ),
                                      ],
                                    ),
                          ),
                        ),

                        // Markdown content
                        _buildMarkdownOrText(context, _currentNoteContent),

                        // Buttons row right under the LLM response area - only show when not regenerating
                        if (!_isRegenerating)
                          Padding(
                            padding: const EdgeInsets.only(top: 20.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Left side - Buttons group (Refresh and Copy Note)
                                Row(
                                  children: [
                                    // Refresh button wrapped with Showcase
                                    TourService.createShowcaseWidget(
                                      context: context,
                                      key: ShowcaseKeys.noteDetailRefreshButton,
                                      description:
                                          "Get an updated response 🔄 It will weave in all your newest ideas. 🌱\n\nNeed to adjust the prompt itself? Just click 💬 the area above to edit.",
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color:
                                                VojiTheme.colorsOf(
                                                  context,
                                                ).border,
                                            width: 1,
                                          ),
                                          borderRadius: BorderRadius.zero,
                                        ),
                                        child: TextButton.icon(
                                          icon: Icon(
                                            Icons.refresh,
                                            color:
                                                VojiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                          label: Text(
                                            'Refresh',
                                            style: TextStyle(
                                              color:
                                                  VojiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ),
                                          onPressed:
                                              () => _regenerateNote(
                                                isAutomatic: false,
                                              ),
                                          style: TextButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16.0,
                                              vertical: 8.0,
                                            ),
                                            shape: const RoundedRectangleBorder(
                                              borderRadius: BorderRadius.zero,
                                            ),
                                            foregroundColor:
                                                VojiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                        ),
                                      ),
                                    ),

                                    // Small space between buttons
                                    const SizedBox(width: 8.0),

                                    // Simple copy button
                                    Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color:
                                              VojiTheme.colorsOf(
                                                context,
                                              ).border,
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.zero,
                                      ),
                                      child: TextButton.icon(
                                        icon: Icon(
                                          Icons.copy,
                                          color:
                                              VojiTheme.colorsOf(
                                                context,
                                              ).textPrimary,
                                        ),
                                        label: Text(
                                          'Copy',
                                          style: TextStyle(
                                            color:
                                                VojiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                        ),
                                        onPressed: () {
                                          // Copy plain text (without markdown)
                                          Clipboard.setData(
                                            ClipboardData(
                                              text: _cleanMarkdownForClipboard(
                                                _currentNoteContent,
                                              ),
                                            ),
                                          );

                                          // Show success message
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'Copied to clipboard',
                                              ),
                                              duration: Duration(seconds: 1),
                                            ),
                                          );
                                        },
                                        style: TextButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16.0,
                                            vertical: 8.0,
                                          ),
                                          shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.zero,
                                          ),
                                          foregroundColor:
                                              VojiTheme.colorsOf(
                                                context,
                                              ).textPrimary,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // Right side - Delete button
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: VojiTheme.colorsOf(context).border,
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.zero,
                                  ),
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.delete,
                                      color:
                                          VojiTheme.colorsOf(
                                            context,
                                          ).textPrimary,
                                    ),
                                    onPressed: _deleteNote,
                                    padding: const EdgeInsets.all(8.0),
                                    constraints: const BoxConstraints(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build a loading scaffold
  Widget _buildLoadingScaffold(BuildContext context) {
    // Use a neutral color for the app bar
    final appBarColor = VojiTheme.colorsOf(context).ideabookColors['none']!;
    final textColor = VojiTheme.getTextColorForIdeabookColor(
      context,
      0,
    ); // 0 is 'none'

    return Scaffold(
      appBar: AppBar(
        // Custom title widget with explicit layout
        title: null, // Remove default title
        automaticallyImplyLeading: false, // Don't automatically add back button
        backgroundColor: appBarColor,
        flexibleSpace: SafeArea(
          child: Row(
            children: [
              // Back button (left-aligned)
              IconButton(
                icon: Icon(Icons.arrow_back, color: textColor),
                onPressed: () => Navigator.of(context).pop(),
              ),

              // Title (right-aligned)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(
                    right: 16.0,
                  ), // Add right padding
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Loading...',
                      style: GoogleFonts.afacad(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: textColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        // Use a Column to ensure the content takes up the full available space
        child: Column(
          children: [
            // Expanded ensures the scroll view takes all available space
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(16.0, 20.0, 16.0, 0.0),
                // Use BouncingScrollPhysics to allow bouncing even when content doesn't fill the screen
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                ),
                // ClampingBehavior.none ensures it always bounces back to original position
                clipBehavior: Clip.none,
                child: Center(child: const CircularProgressIndicator()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build an error scaffold
  Widget _buildErrorScaffold(BuildContext context, Object error) {
    // Use a neutral color for the app bar
    final appBarColor = VojiTheme.colorsOf(context).ideabookColors['none']!;
    final textColor = VojiTheme.getTextColorForIdeabookColor(
      context,
      0,
    ); // 0 is 'none'

    return Scaffold(
      appBar: AppBar(
        // Custom title widget with explicit layout
        title: null, // Remove default title
        automaticallyImplyLeading: false, // Don't automatically add back button
        backgroundColor: appBarColor,
        flexibleSpace: SafeArea(
          child: Row(
            children: [
              // Back button (left-aligned)
              IconButton(
                icon: Icon(Icons.arrow_back, color: textColor),
                onPressed: () => Navigator.of(context).pop(),
              ),

              // Title (right-aligned)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(
                    right: 16.0,
                  ), // Add right padding
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Error',
                      style: GoogleFonts.afacad(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: textColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        // Use a Column to ensure the content takes up the full available space
        child: Column(
          children: [
            // Expanded ensures the scroll view takes all available space
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(16.0, 20.0, 16.0, 0.0),
                // Use BouncingScrollPhysics to allow bouncing even when content doesn't fill the screen
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                ),
                // ClampingBehavior.none ensures it always bounces back to original position
                clipBehavior: Clip.none,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: VojiTheme.colorsOf(context).error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading note',
                        style: VojiTheme.textStylesOf(context).bodyLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: VojiTheme.textStylesOf(context).bodySmall,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a not found scaffold
  Widget _buildNotFoundScaffold(BuildContext context) {
    // Use a neutral color for the app bar
    final appBarColor = VojiTheme.colorsOf(context).ideabookColors['none']!;
    final textColor = VojiTheme.getTextColorForIdeabookColor(
      context,
      0,
    ); // 0 is 'none'

    return Scaffold(
      appBar: AppBar(
        // Custom title widget with explicit layout
        title: null, // Remove default title
        automaticallyImplyLeading: false, // Don't automatically add back button
        backgroundColor: appBarColor,
        flexibleSpace: SafeArea(
          child: Row(
            children: [
              // Back button (left-aligned)
              IconButton(
                icon: Icon(Icons.arrow_back, color: textColor),
                onPressed: () => Navigator.of(context).pop(),
              ),

              // Title (right-aligned)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(
                    right: 16.0,
                  ), // Add right padding
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Not Found',
                      style: GoogleFonts.afacad(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: textColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        // Use a Column to ensure the content takes up the full available space
        child: Column(
          children: [
            // Expanded ensures the scroll view takes all available space
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(16.0, 20.0, 16.0, 0.0),
                // Use BouncingScrollPhysics to allow bouncing even when content doesn't fill the screen
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                ),
                // ClampingBehavior.none ensures it always bounces back to original position
                clipBehavior: Clip.none,
                child: Center(
                  child: Text(
                    'Note not found',
                    style: VojiTheme.textStylesOf(context).bodyLarge,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Regenerate the note content by sending the current title to the LLM
  /// If [isAutomatic] is true, this was triggered automatically after a title update
  Future<void> _regenerateNote({bool isAutomatic = false}) async {
    Logger.debug(
      '_regenerateNote called with isAutomatic=$isAutomatic, title="$_currentNoteTitle"',
    );

    // Store local references before any async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final errorColor = VojiTheme.colorsOf(context).error;

    // Get appropriate text styles for regular and error messages
    final snackBarTextColor = VojiTheme.getSnackBarTextColor(context);
    final errorSnackBarTextColor = VojiTheme.getSnackBarTextColor(
      context,
      backgroundColor: errorColor,
    );

    try {
      // Set regenerating state to true and start animation
      setState(() {
        _isRegenerating = true;
        _dotsController.forward();
      });

      // Get the current note title (which could be updated by the user)
      final noteTitle = _currentNoteTitle;

      // Get the ideabook
      final ideabook = await ref.read(
        ideabookProvider(widget.ideabookId).future,
      );
      if (ideabook == null) {
        throw Exception('Ideabook not found');
      }

      // Get all ideas for the ideabook using the Firestore service
      final firestoreService = ref.read(firestoreServiceProvider);
      final ideas =
          await firestoreService.listenToIdeas(widget.ideabookId).first;

      Logger.debug('Regenerating note with ${ideas.length} ideas');

      // Format the ideas for the prompt with date only (no time)
      final ideasText =
          ideas.isEmpty
              ? "No ideas found in this ideabook yet."
              : ideas
                  .map((idea) {
                    // Extract only the date part (YYYY-MM-DD)
                    final dateOnly = idea.createdAt.toString().split(' ')[0];
                    return '$dateOnly | ${idea.content}';
                  })
                  .join('\n');

      // Get the note regeneration prompt template from Remote Config and replace variables
      final llmPrompts = ref.read(llmPromptsProvider);
      final promptTemplate = llmPrompts.getNoteRegenerationPrompt();
      final promptContent = llmPrompts.replaceVariables(promptTemplate, {
        'ideabook_name': ideabook.name,
        'ideas_text': ideasText,
        'note_title': noteTitle,
      });

      // Send the prompt to the LLM using Gemini Chat Service
      final geminiChatService = ref.read(geminiChatServiceProvider);
      final promptMessage = ChatMessage(
        id: IdUtils.generateId(),
        role: MessageRole.user,
        content: promptContent,
        timestamp: DateTime.now(),
      );

      // Send the message to Gemini
      final response = await geminiChatService.chat([promptMessage]);

      // Stop regenerating state regardless of response
      setState(() {
        _isRegenerating = false;
        _dotsController.stop();
      });

      if (response.isSuccess && response.content != null) {
        // The response content should already be parsed by the Gemma service
        // But we'll double-check just in case
        String responseContent = response.content!;

        // Check if the content might still be JSON or wrapped in markdown code blocks
        String contentToProcess = responseContent;

        // Check for markdown code blocks with ```json or ``` pattern
        if (contentToProcess.trim().startsWith('```')) {
          Logger.debug(
            'Content appears to be wrapped in markdown code blocks, extracting...',
          );

          // Find the end of the code block
          final endBlockIndex = contentToProcess.lastIndexOf('```');
          if (endBlockIndex > 3) {
            // Extract content between the code blocks
            final startContentIndex =
                contentToProcess.indexOf(
                  '\n',
                  contentToProcess.indexOf('```'),
                ) +
                1;
            if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
              contentToProcess =
                  contentToProcess
                      .substring(startContentIndex, endBlockIndex)
                      .trim();
              Logger.debug(
                'Extracted content from markdown code block: ${contentToProcess.substring(0, contentToProcess.length.clamp(0, 100))}${contentToProcess.length > 100 ? "..." : ""}',
              );
            }
          }
        }

        // Enhanced JSON detection and parsing
        bool foundResponse = false;

        // First check: direct JSON format
        if (contentToProcess.trim().startsWith('{') &&
            contentToProcess.trim().endsWith('}')) {
          try {
            // Try to parse the JSON response
            final jsonResponse =
                json.decode(contentToProcess) as Map<String, dynamic>;

            // Extract the response content if it exists
            if (jsonResponse.containsKey('response')) {
              responseContent = jsonResponse['response'] as String;
              foundResponse = true;
              Logger.debug('Successfully parsed JSON response:');
              Logger.debug('Response content: $responseContent');
            } else {
              // Try to find response field in nested objects
              jsonResponse.forEach((key, value) {
                if (value is Map<String, dynamic> &&
                    value.containsKey('response')) {
                  responseContent = value['response'] as String;
                  foundResponse = true;
                  Logger.debug(
                    'Successfully extracted response field from nested JSON',
                  );
                }
              });
            }
          } catch (e) {
            // If parsing fails, try more aggressive approaches
            Logger.debug(
              'Initial JSON parsing failed: $e, trying alternative approaches',
            );
          }
        }

        // Second check: Try to find any JSON-like structure in the content
        if (!foundResponse) {
          // Look for patterns like {"response": "..."}
          final responsePattern = RegExp(
            r'[\s\S]*?"response"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
          );
          final match = responsePattern.firstMatch(contentToProcess);

          if (match != null && match.groupCount >= 1) {
            responseContent = match.group(1) ?? responseContent;
            foundResponse = true;
            Logger.debug('Extracted response using regex pattern matching');
          }
        }

        // Third check: If it still looks like JSON but we couldn't extract the response
        if (!foundResponse &&
            contentToProcess.contains('"response"') &&
            contentToProcess.contains('{') &&
            contentToProcess.contains('}')) {
          try {
            // Try to clean up the JSON string
            String cleanedJson =
                contentToProcess
                    .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                    .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                    .trim();

            // Find the start and end of what looks like a JSON object
            int startIdx = cleanedJson.indexOf('{');
            int endIdx = cleanedJson.lastIndexOf('}') + 1;

            if (startIdx >= 0 && endIdx > startIdx) {
              cleanedJson = cleanedJson.substring(startIdx, endIdx);

              // Try to parse the cleaned JSON
              final jsonResponse =
                  json.decode(cleanedJson) as Map<String, dynamic>;

              if (jsonResponse.containsKey('response')) {
                responseContent = jsonResponse['response'] as String;
                foundResponse = true;
                Logger.debug(
                  'Successfully extracted response field from cleaned JSON',
                );
              }
            }
          } catch (e) {
            Logger.debug('Cleaned JSON parsing failed: $e');
          }
        }

        // If all parsing attempts failed but content still looks like JSON, show error message
        if (!foundResponse &&
                (contentToProcess.trim().startsWith('{') &&
                    contentToProcess.trim().endsWith('}')) ||
            contentToProcess.contains('"response"')) {
          responseContent =
              "Error: Could not parse the AI response. Please try again.";
          Logger.error('Failed to parse JSON response after multiple attempts');
        }

        // Create a new note with the updated content and preserving the current title
        final updatedNote = widget.note.copyWith(
          title: _currentNoteTitle, // Use the current title from UI
          content: responseContent,
          updatedAt: DateTime.now(),
        );

        // Update the note in Firestore
        final firestoreService = ref.read(firestoreServiceProvider);
        final success = await firestoreService.updateNote(widget.ideabookId, updatedNote);

        if (success) {
          if (mounted) {
            // Update the local state with the new content
            setState(() {
              _currentNoteContent = responseContent;
            });

            // Don't show a notification after regeneration
            // Just log the successful regeneration
            Logger.debug(
              'Note regenerated successfully (automatic: $isAutomatic)',
            );
          }
        } else {
          if (mounted) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  'Failed to update note content',
                  style: TextStyle(color: snackBarTextColor),
                ),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      } else {
        // Handle error
        Logger.error('Error from Gemini API', response.errorMessage);
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'Error from AI: ${response.errorMessage ?? "Something went wrong. Please try again later."}',
                style: TextStyle(color: errorSnackBarTextColor),
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: errorColor,
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error regenerating note', e);

      // Stop regenerating state
      setState(() {
        _isRegenerating = false;
        _dotsController.stop();
      });

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Error regenerating note: $e',
              style: TextStyle(color: errorSnackBarTextColor),
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: errorColor,
          ),
        );
      }
    }
  }

  /// Clean markdown syntax for clipboard copying
  String _cleanMarkdownForClipboard(String text) {
    String cleaned = text;

    // Remove bold markers
    cleaned = RegExp(r'\*\*(.*?)\*\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove italic markers
    cleaned = RegExp(r'\*(.*?)\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    cleaned = RegExp(r'_(.*?)_')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove headers
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s+'), '');

    // Remove code blocks
    cleaned = cleaned.replaceAll(RegExp(r'```.*?```', dotAll: true), '');

    // Remove inline code
    cleaned = RegExp(r'`(.*?)`')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove bullet points
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '');

    // Remove numbered lists
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // Remove blockquotes
    cleaned = cleaned.replaceAll(RegExp(r'^\s*>\s+', multiLine: true), '');

    // Remove horizontal rules
    cleaned = cleaned.replaceAll(
      RegExp(r'^\s*[-*_]{3,}\s*$', multiLine: true),
      '',
    );

    // Remove links but keep the text
    cleaned = RegExp(r'\[(.*?)\]\(.*?\)')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove images
    cleaned = cleaned.replaceAll(RegExp(r'!\[.*?\]\(.*?\)'), '');

    return cleaned;
  }

  /// Build markdown content with proper handling of all characters
  Widget _buildMarkdownOrText(BuildContext context, String content) {
    // If regenerating, show the thinking indicator instead of content
    if (_isRegenerating) {
      // Build the dots string
      final dots = '.' * _dotCount;

      return Text(
        'Thinking$dots',
        style: VojiTheme.textStylesOf(context).bodyMedium,
      );
    }

    try {
      // Check if the content might be JSON and extract the response field if it exists
      String processedContent = content;

      // Check if the content might be JSON or wrapped in markdown code blocks
      String contentToProcess = content;

      // Check for markdown code blocks with ```json or ``` pattern
      if (contentToProcess.trim().startsWith('```')) {
        Logger.debug(
          'Content appears to be wrapped in markdown code blocks, extracting...',
        );

        // Find the end of the code block
        final endBlockIndex = contentToProcess.lastIndexOf('```');
        if (endBlockIndex > 3) {
          // Extract content between the code blocks
          final startContentIndex =
              contentToProcess.indexOf('\n', contentToProcess.indexOf('```')) +
              1;
          if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
            contentToProcess =
                contentToProcess
                    .substring(startContentIndex, endBlockIndex)
                    .trim();
            Logger.debug('Extracted content from markdown code block');
          }
        }
      }

      // Enhanced JSON detection and parsing
      bool foundResponse = false;

      // First check: direct JSON format
      if (contentToProcess.trim().startsWith('{') &&
          contentToProcess.trim().endsWith('}')) {
        try {
          // Try to parse the JSON response
          final jsonResponse =
              json.decode(contentToProcess) as Map<String, dynamic>;

          // Extract the response content if it exists
          if (jsonResponse.containsKey('response')) {
            processedContent = jsonResponse['response'] as String;
            foundResponse = true;
            Logger.debug(
              'Successfully extracted response field from JSON in note detail',
            );
          } else {
            // Try to find response field in nested objects
            jsonResponse.forEach((key, value) {
              if (value is Map<String, dynamic> &&
                  value.containsKey('response')) {
                processedContent = value['response'] as String;
                foundResponse = true;
                Logger.debug(
                  'Successfully extracted response field from nested JSON in note detail',
                );
              }
            });
          }
        } catch (e) {
          // If parsing fails, try more aggressive approaches
          Logger.debug(
            'Initial JSON parsing failed in note detail: $e, trying alternative approaches',
          );
        }
      }

      // Second check: Try to find any JSON-like structure in the content
      if (!foundResponse) {
        // Look for patterns like {"response": "..."}
        final responsePattern = RegExp(
          r'[\s\S]*?"response"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
        );
        final match = responsePattern.firstMatch(contentToProcess);

        if (match != null && match.groupCount >= 1) {
          processedContent = match.group(1) ?? processedContent;
          foundResponse = true;
          Logger.debug(
            'Extracted response using regex pattern matching in note detail',
          );
        }
      }

      // Third check: If it still looks like JSON but we couldn't extract the response
      if (!foundResponse &&
          contentToProcess.contains('"response"') &&
          contentToProcess.contains('{') &&
          contentToProcess.contains('}')) {
        try {
          // Try to clean up the JSON string
          String cleanedJson =
              contentToProcess
                  .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                  .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                  .trim();

          // Find the start and end of what looks like a JSON object
          int startIdx = cleanedJson.indexOf('{');
          int endIdx = cleanedJson.lastIndexOf('}') + 1;

          if (startIdx >= 0 && endIdx > startIdx) {
            cleanedJson = cleanedJson.substring(startIdx, endIdx);

            // Try to parse the cleaned JSON
            final jsonResponse =
                json.decode(cleanedJson) as Map<String, dynamic>;

            if (jsonResponse.containsKey('response')) {
              processedContent = jsonResponse['response'] as String;
              foundResponse = true;
              Logger.debug(
                'Successfully extracted response field from cleaned JSON in note detail',
              );
            }
          }
        } catch (e) {
          Logger.debug('Cleaned JSON parsing failed in note detail: $e');
        }
      }

      // If all parsing attempts failed but content still looks like JSON, show error message
      if (!foundResponse &&
              (contentToProcess.trim().startsWith('{') &&
                  contentToProcess.trim().endsWith('}')) ||
          contentToProcess.contains('"response"')) {
        processedContent =
            "Error: Could not parse the AI response. Please try again.";
        Logger.error(
          'Failed to parse JSON response in note detail after multiple attempts',
        );
      }

      // Use MarkdownBody without selectable text to avoid null check errors
      return MarkdownBody(
        data: processedContent,
        selectable:
            false, // Avoid selectable text to prevent the null check error
        styleSheet: MarkdownStyleSheet(
          p: VojiTheme.textStylesOf(context).bodyMedium,
          h1: VojiTheme.textStylesOf(context).titleLarge,
          h2: VojiTheme.textStylesOf(context).titleMedium,
          h3: VojiTheme.textStylesOf(context).titleSmall,
          code: VojiTheme.textStylesOf(context).bodyMedium.copyWith(
            fontFamily: 'monospace',
            backgroundColor: VojiTheme.colorsOf(context).cardBackground,
          ),
          codeblockDecoration: BoxDecoration(
            color: VojiTheme.colorsOf(context).cardBackground,
            border: Border.all(
              color: VojiTheme.colorsOf(context).border,
              width: 1,
            ),
          ),
        ),
      );
    } catch (e) {
      // If markdown rendering fails, fall back to plain text
      Logger.error('Error rendering markdown in note detail: $e');
      return Text(content, style: VojiTheme.textStylesOf(context).bodyMedium);
    }
  }
}
