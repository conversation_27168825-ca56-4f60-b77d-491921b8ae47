import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:voji/services/auth/auth_providers.dart';
import 'package:voji/services/chat/chat_rate_limit_service_provider.dart';
import 'package:voji/services/subscription/subscription_providers.dart';
import 'package:voji/services/tour/tour_service.dart';
import 'package:voji/ui/providers/auth_provider.dart';
import 'package:voji/ui/providers/passcode_provider.dart';
import 'package:voji/ui/screens/passcode_screen.dart';
import 'package:voji/ui/theme/theme_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/feature_flags.dart';
import 'package:voji/utils/logger.dart';

/// Settings screen
class SettingsScreen extends ConsumerWidget {
  /// Constructor
  const SettingsScreen({super.key});

  /// Show the passcode change screen
  void _showChangePasscodeScreen(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      builder: (bottomSheetContext) => SizedBox(
        height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
        child: PasscodeScreen(
          isChangingPasscode: true,
          onSuccess: () {
            Navigator.of(bottomSheetContext).pop(); // Close the passcode screen
          },
          onCancel: () {
            Navigator.of(bottomSheetContext).pop(); // Close the passcode screen
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the current user
    final user = ref.watch(currentUserProvider);

    // If user is null, show error
    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Settings',
            style: GoogleFonts.afacad(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: VojiTheme.colorsOf(context).textPrimary,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Center(
          child: Text(
            'User not signed in',
            style: VojiTheme.textStylesOf(context).bodyLarge,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: GoogleFonts.afacad(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: VojiTheme.colorsOf(context).textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture and user info
            Row(
              children: [
                // Profile picture
                if (user.photoUrl != null)
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: NetworkImage(user.photoUrl!),
                  )
                else
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: VojiTheme.colorsOf(context).border,
                    child: Icon(
                      Icons.person,
                      size: 30,
                      color: VojiTheme.colorsOf(context).textPrimary,
                    ),
                  ),

                const SizedBox(width: 16),

                // User name and email
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.displayName,
                        style: GoogleFonts.afacad(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: VojiTheme.colorsOf(context).textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.email,
                        style: GoogleFonts.afacad(
                          fontSize: 14,
                          color: VojiTheme.colorsOf(context).textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // Sign out button
                IconButton(
                  icon: Icon(
                    Icons.logout,
                    color: VojiTheme.colorsOf(context).textPrimary,
                  ),
                  onPressed: () async {
                    // Show confirmation dialog
                    final bool? confirmSignOut = await showDialog<bool>(
                      context: context,
                      builder: (BuildContext dialogContext) {
                        return AlertDialog(
                          title: Text(
                            'Sign Out',
                            style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
                          ),
                          content: Text(
                            'Are you sure you want to sign out?',
                            style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                            side: BorderSide(
                              color: VojiTheme.colorsOf(dialogContext).border,
                              width: 1,
                            ),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(dialogContext).pop(false),
                              child: Text(
                                'Cancel',
                                style: VojiTheme.textStylesOf(dialogContext).buttonText,
                              ),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(dialogContext).pop(true),
                              child: Text(
                                'Sign Out',
                                style: VojiTheme.textStylesOf(dialogContext).buttonText,
                              ),
                            ),
                          ],
                        );
                      },
                    );

                    // If user confirmed, proceed with sign out
                    if (confirmSignOut == true) {
                      try {
                        final signOut = ref.read(signOutProvider);
                        await signOut();
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Signed out successfully',
                                style: GoogleFonts.afacad(),
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        Logger.error('Error signing out', e);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Error signing out: ${e.toString()}',
                                style: GoogleFonts.afacad(),
                              ),
                              backgroundColor: VojiTheme.colorsOf(context).error,
                            ),
                          );
                        }
                      }
                    }
                  },
                ),
              ],
            ),

            const SizedBox(height: 40),

            // Change passcode button
            Consumer(
              builder: (context, ref, child) {
                final isPasscodeSet = ref.watch(passcodeSetCachedProvider).isSet;

                // Only show the option if a passcode is set
                if (isPasscodeSet) {
                  return Column(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(
                            color: VojiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                        ),
                        child: InkWell(
                          onTap: () {
                            _showChangePasscodeScreen(context);
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.password,
                                  color: VojiTheme.colorsOf(context).textPrimary,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Change passcode',
                                  style: GoogleFonts.afacad(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: VojiTheme.colorsOf(context).textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),

            // Theme section
            Text(
              'Theme',
              style: GoogleFonts.afacad(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: VojiTheme.colorsOf(context).textPrimary,
              ),
            ),

            const SizedBox(height: 16),

            // Theme selection buttons - single three-way selector
            Consumer(
              builder: (context, ref, child) {
                final themeMode = ref.watch(themeModeProvider);

                return Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: VojiTheme.colorsOf(context).border,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Light theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.wb_sunny,
                          label: 'Light',
                          isSelected: themeMode == ThemeMode.light,
                          onTap: () {
                            ref.read(themeModeProvider.notifier).setThemeMode(ThemeMode.light);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // Dark theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.nightlight_round,
                          label: 'Dark',
                          isSelected: themeMode == ThemeMode.dark,
                          onTap: () {
                            ref.read(themeModeProvider.notifier).setThemeMode(ThemeMode.dark);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // System theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.settings,
                          label: 'System',
                          isSelected: themeMode == ThemeMode.system,
                          onTap: () {
                            ref.read(themeModeProvider.notifier).setThemeMode(ThemeMode.system);
                          },
                          showRightBorder: false,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Debug settings (only shown in debug mode)
            if (FeatureFlags.showDebugOptions) ...[
              Text(
                'Debug Settings',
                style: GoogleFonts.afacad(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: VojiTheme.colorsOf(context).textPrimary,
                ),
              ),

              const SizedBox(height: 16),

              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: VojiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Store the context before the async gap
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    // Reset all tours
                    TourService.resetAllTours().then((result) {
                      // Show a snackbar to confirm
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            result
                              ? 'Tour tooltip states cleared. Tooltips will appear again when their conditions are met.'
                              : 'Failed to clear tour tooltip states'
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.restart_alt,
                          color: VojiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Tour States',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: VojiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Rate limit debug status button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: VojiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Debug rate limit status
                    Logger.debug('===== DEBUGGING RATE LIMIT STATUS =====');
                    ref
                        .read(chatRateLimitServiceProvider)
                        .debugRateLimitStatus()
                        .then((_) {
                          // Show a snackbar to confirm if context is still valid
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Rate limit status logged to console',
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bug_report,
                          color: VojiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Debug Rate Limit Status',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: VojiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Clear rate limit logs button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: VojiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Clear rate limit logs
                    Logger.debug(
                      '===== CLEARING RATE LIMIT LOGS FOR TESTING =====',
                    );
                    ref.read(chatRateLimitServiceProvider).clearMessageLog().then(
                      (success) {
                        Logger.debug('Rate limit logs cleared: $success');

                        // Show a snackbar to confirm if context is still valid
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Rate limit logs cleared for testing',
                              ),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.clear_all,
                          color: VojiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Rate Limit Logs',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: VojiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],

            // Add some space before the App User ID
            const SizedBox(height: 40),

            // App User ID display at the bottom - centered
            Center(
              child: GestureDetector(
                onTap: () async {
                  try {
                    final appUserIdAsync = ref.read(appUserIdProvider);
                    final appUserId = appUserIdAsync.value;

                    if (appUserId != null) {
                      await Clipboard.setData(ClipboardData(text: appUserId));
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'App User ID copied to clipboard',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    Logger.error('Error copying app user ID', e);
                  }
                },
                child: Text(
                  'App User ID: ${ref.watch(appUserIdProvider).value ?? 'Loading...'}',
                  style: GoogleFonts.afacad(
                    fontSize: 12,
                    color: VojiTheme.colorsOf(context).textSecondary,
                  ),
                ),
              ),
            ),

            // Add some bottom padding
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Build a theme selection segment for the three-way selector
  Widget _buildThemeSegment({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool showRightBorder,
  }) {
    // Determine background color based on selection and theme
    Color backgroundColor;
    if (isSelected) {
      // Use a stronger contrast background for selected state
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
      backgroundColor = isDarkTheme
        ? VojiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.25)
        : VojiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.15);
    } else {
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          right: showRightBorder ? BorderSide(
            color: VojiTheme.colorsOf(context).border,
            width: 1,
          ) : BorderSide.none,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: VojiTheme.colorsOf(context).textPrimary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.afacad(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: VojiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
