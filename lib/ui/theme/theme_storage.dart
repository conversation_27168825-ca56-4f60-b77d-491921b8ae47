import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for storing and retrieving theme preferences
class ThemeStorage {
  static const String _themeKey = 'voji_theme_mode';

  /// Save the current theme mode to persistent storage
  static Future<void> saveThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    final value = _themeModeToString(mode);
    await prefs.setString(_themeKey, value);
  }

  /// Load the saved theme mode from persistent storage
  /// Returns ThemeMode.system if no theme is saved (default)
  static Future<ThemeMode> loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_themeKey);

      if (value == null) {
        return ThemeMode.system; // Default to system theme
      }

      return _stringToThemeMode(value);
    } catch (e) {
      // If there's an error, return the default theme
      return ThemeMode.system;
    }
  }

  // Helper method to convert ThemeMode to String
  static String _themeModeToString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  // Helper method to convert String to ThemeMode
  static ThemeMode _stringToThemeMode(String value) {
    switch (value) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.system; // Default to system theme
    }
  }
}
