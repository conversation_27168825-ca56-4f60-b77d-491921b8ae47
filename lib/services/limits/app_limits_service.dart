import 'package:voji/services/remote_config/remote_config_service.dart';
import 'package:voji/utils/logger.dart';

/// Service for managing app limits from Remote Config
/// This service provides access to all app limits based on user tier
class AppLimitsService {
  final RemoteConfigService _remoteConfigService;

  AppLimitsService(this._remoteConfigService);

  /// Get the maximum number of ideabooks for the user tier
  int getMaxIdeabooks({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_max_ideabooks' : 'free_max_ideabooks';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the maximum number of ideas per ideabook for the user tier
  int getMaxIdeasPerIdeabook({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_max_ideas_per_ideabook' : 'free_max_ideas_per_ideabook';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the maximum number of notes per ideabook for the user tier
  int getMaxNotesPerIdeabook({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_max_notes_per_ideabook' : 'free_max_notes_per_ideabook';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the daily chat limit for the user tier
  int getChatLimitDaily({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_chat_limit_daily' : 'free_chat_limit_daily';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the monthly chat limit for the user tier
  int getChatLimitMonthly({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_chat_limit_monthly' : 'free_chat_limit_monthly';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the chat rate limit per minute for pro users (free users don't have rate limits)
  int? getChatRateLimitMinute({String? userTier}) {
    if (userTier == 'pro') {
      return _remoteConfigService.getInt('pro_chat_rate_limit_minute', userTier: userTier);
    }
    return null; // Free users don't have rate limits
  }

  /// Get the maximum audio recording length in seconds for the user tier
  int getAudioRecordingLengthSeconds({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_audio_recording_length_seconds' : 'free_audio_recording_length_seconds';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the maximum words in ideabook name for the user tier
  int getIdeabookNameMaxWords({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_ideabook_name_max_words' : 'free_ideabook_name_max_words';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the maximum words per idea for the user tier
  int getIdeaMaxWords({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_idea_max_words' : 'free_idea_max_words';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Get the maximum words per chat input for the user tier
  int getChatInputMaxWords({String? userTier}) {
    final key = userTier == 'pro' ? 'pro_chat_input_max_words' : 'free_chat_input_max_words';
    return _remoteConfigService.getInt(key, userTier: userTier);
  }

  /// Log all current limits for debugging
  void logCurrentLimits({String? userTier}) {
    final tier = userTier ?? 'unknown';
    Logger.debug('===== APP LIMITS CONFIGURATION ($tier tier) =====');
    Logger.debug('Max ideabooks: ${getMaxIdeabooks(userTier: userTier)}');
    Logger.debug('Max ideas per ideabook: ${getMaxIdeasPerIdeabook(userTier: userTier)}');
    Logger.debug('Max notes per ideabook: ${getMaxNotesPerIdeabook(userTier: userTier)}');
    Logger.debug('Chat limit daily: ${getChatLimitDaily(userTier: userTier)}');
    Logger.debug('Chat limit monthly: ${getChatLimitMonthly(userTier: userTier)}');
    final rateLimitMinute = getChatRateLimitMinute(userTier: userTier);
    if (rateLimitMinute != null) {
      Logger.debug('Chat rate limit per minute: $rateLimitMinute');
    }
    Logger.debug('Audio recording length (seconds): ${getAudioRecordingLengthSeconds(userTier: userTier)}');
    Logger.debug('Ideabook name max words: ${getIdeabookNameMaxWords(userTier: userTier)}');
    Logger.debug('Idea max words: ${getIdeaMaxWords(userTier: userTier)}');
    Logger.debug('Chat input max words: ${getChatInputMaxWords(userTier: userTier)}');
    Logger.debug('====================================');
  }
}
