import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/limits/app_limits_providers.dart';
import 'package:voji/services/subscription/subscription_providers.dart';
import 'package:voji/utils/logger.dart';

/// Helper service for repositories to check limits
/// This provides a bridge between the old repository pattern and the new limits system
class RepositoryLimitsHelper {
  final ProviderContainer _container;

  RepositoryLimitsHelper(this._container);

  /// Check if the user has reached the maximum number of ideabooks
  Future<bool> isUserAtIdeabookLimit() async {
    try {
      final maxIdeabooks = await _container.read(maxIdeabooksProvider.future);
      // This would need to be implemented by getting the actual count
      // For now, we'll return false as a placeholder
      Logger.debug('RepositoryLimitsHelper: Max ideabooks for user: $maxIdeabooks');
      return false; // TODO: Implement actual count check
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check ideabook limit', e);
      return false; // In case of error, assume not at limit to avoid blocking the user
    }
  }

  /// Check if an ideabook has reached the maximum number of ideas
  Future<bool> isIdeabookFullOfIdeas(String ideabookId) async {
    try {
      final maxIdeas = await _container.read(maxIdeasPerIdeabookProvider.future);
      // This would need to be implemented by getting the actual count
      // For now, we'll return false as a placeholder
      Logger.debug('RepositoryLimitsHelper: Max ideas per ideabook for user: $maxIdeas');
      return false; // TODO: Implement actual count check
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check ideas limit', e);
      return false; // In case of error, assume not full to avoid blocking the user
    }
  }

  /// Check if an ideabook has reached the maximum number of notes
  Future<bool> isIdeabookFullOfNotes(String ideabookId) async {
    try {
      final maxNotes = await _container.read(maxNotesPerIdeabookProvider.future);
      // This would need to be implemented by getting the actual count
      // For now, we'll return false as a placeholder
      Logger.debug('RepositoryLimitsHelper: Max notes per ideabook for user: $maxNotes');
      return false; // TODO: Implement actual count check
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check notes limit', e);
      return false; // In case of error, assume not full to avoid blocking the user
    }
  }

  /// Get the maximum number of ideabooks for the current user
  Future<int> getMaxIdeabooks() async {
    try {
      return await _container.read(maxIdeabooksProvider.future);
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get max ideabooks', e);
      return 1000; // Fallback to pro tier limit
    }
  }

  /// Get the maximum number of ideas per ideabook for the current user
  Future<int> getMaxIdeasPerIdeabook() async {
    try {
      return await _container.read(maxIdeasPerIdeabookProvider.future);
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get max ideas per ideabook', e);
      return 100; // Fallback to pro tier limit
    }
  }

  /// Get the maximum number of notes per ideabook for the current user
  Future<int> getMaxNotesPerIdeabook() async {
    try {
      return await _container.read(maxNotesPerIdeabookProvider.future);
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get max notes per ideabook', e);
      return 100; // Fallback to pro tier limit
    }
  }

  /// Get the user tier
  Future<String> getUserTier() async {
    try {
      return await _container.read(userTierProvider.future);
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get user tier', e);
      return 'free'; // Fallback to free tier
    }
  }

  /// Create an exception message for when the ideabook limit is reached
  Future<String> getIdeabookLimitExceptionMessage() async {
    final maxIdeabooks = await getMaxIdeabooks();
    return 'Maximum number of ideabooks reached ($maxIdeabooks).';
  }

  /// Create an exception message for when the ideas limit is reached
  Future<String> getIdeasLimitExceptionMessage() async {
    final maxIdeas = await getMaxIdeasPerIdeabook();
    return 'Ideabook is full. Maximum of $maxIdeas ideas reached.';
  }

  /// Create an exception message for when the notes limit is reached
  Future<String> getNotesLimitExceptionMessage() async {
    final maxNotes = await getMaxNotesPerIdeabook();
    return 'Ideabook is full. Maximum of $maxNotes notes reached.';
  }
}
