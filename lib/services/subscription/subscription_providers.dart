import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:voji/services/auth/auth_providers.dart';
import 'package:voji/services/subscription/revenue_cat_service.dart';
import 'package:voji/services/subscription/user_tier_service.dart';
import 'package:voji/utils/logger.dart';

/// Provider for the RevenueCat service
final revenueCatServiceProvider = Provider<RevenueCatService>((ref) {
  return RevenueCatService();
});

/// Provider for the user tier service
final userTierServiceProvider = Provider<UserTierService>((ref) {
  return UserTierService.instance;
});

/// Provider for checking if the user is a pro user
final isProUserProvider = FutureProvider<bool>((ref) async {
  // Ensure RevenueCat is initialized
  ref.watch(ensureRevenueCatInitializedProvider);

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are not a pro user
  if (user == null) {
    return false;
  }

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.isProUser();
  } catch (e) {
    Logger.error('Failed to check if user is pro', e);
    return false;
  }
});

/// Provider for getting the user tier as a string
final userTierProvider = FutureProvider<String>((ref) async {
  // Ensure RevenueCat is initialized
  ref.watch(ensureRevenueCatInitializedProvider);

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are a free user
  if (user == null) {
    return 'free';
  }

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.getUserTier();
  } catch (e) {
    Logger.error('Failed to get user tier', e);
    return 'free';
  }
});

/// Provider to ensure RevenueCat is initialized when the user is signed in
final ensureRevenueCatInitializedProvider = Provider<void>((ref) {
  // Watch the current Firebase user
  final userAsync = ref.watch(firebaseUserProvider);
  final revenueCatService = ref.watch(revenueCatServiceProvider);

  // Store the previous user ID to detect changes
  String? previousUserId;

  // Initialize RevenueCat when the user is signed in
  userAsync.whenData((user) async {
    if (user != null) {
      final currentUserId = user.uid;

      // Check if the user ID has changed
      if (previousUserId != currentUserId) {
        Logger.debug('User is signed in, initializing RevenueCat with user ID: $currentUserId');
        await revenueCatService.initialize(userId: currentUserId);

        // Update the previous user ID
        previousUserId = currentUserId;
      }
    } else {
      Logger.debug('User is not signed in, skipping RevenueCat initialization');
      // Reset the previous user ID when signed out
      previousUserId = null;
    }
  });

  return;
});

/// Provider for customer info from RevenueCat
final customerInfoProvider = FutureProvider<CustomerInfo?>((ref) async {
  // Ensure RevenueCat is initialized
  ref.watch(ensureRevenueCatInitializedProvider);

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  try {
    final revenueCatService = ref.watch(revenueCatServiceProvider);
    return await revenueCatService.getCustomerInfo();
  } catch (e) {
    Logger.error('Failed to get customer info', e);
    return null;
  }
});

/// Provider for offerings from RevenueCat
final offeringsProvider = FutureProvider<Offerings?>((ref) async {
  // Ensure RevenueCat is initialized
  ref.watch(ensureRevenueCatInitializedProvider);

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  try {
    final revenueCatService = ref.watch(revenueCatServiceProvider);
    return await revenueCatService.getOfferings();
  } catch (e) {
    Logger.error('Failed to get offerings', e);
    return null;
  }
});

/// Provider for the app user ID from RevenueCat
final appUserIdProvider = FutureProvider<String?>((ref) async {
  // Ensure RevenueCat is initialized
  ref.watch(ensureRevenueCatInitializedProvider);

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  try {
    final revenueCatService = ref.watch(revenueCatServiceProvider);
    return await revenueCatService.getAppUserId();
  } catch (e) {
    Logger.error('Failed to get app user ID', e);
    return null;
  }
});
