import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/llm/gemini_chat_service.dart';
import 'package:voji/services/llm/gemma_service.dart';
import 'package:voji/services/llm/grok_service.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/services/remote_config/remote_config_providers.dart';

/// Provider for the Gemini LLM service
final geminiServiceProvider = Provider<GeminiService>((ref) {
  // Get the cloud function service
  final cloudFunctionService = ref.watch(cloudFunctionServiceProvider);

  // Get the Remote Config providers
  final llmPrompts = ref.watch(llmPromptsProvider);
  final llmModelConfig = ref.watch(llmModelConfigProvider);

  // Create the Gemini service with the cloud function service and Remote Config
  final service = GeminiService(
    cloudFunctionService: cloudFunctionService,
    llmPrompts: llmPrompts,
    llmModelConfig: llmModelConfig,
    // TODO: Add user tier detection here when subscription system is integrated
    userTier: null,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the Gemma LLM service (kept for backward compatibility)
final gemmaServiceProvider = Provider<GemmaService>((ref) {
  // Get the cloud function service
  final cloudFunctionService = ref.watch(cloudFunctionServiceProvider);

  // Create the Gemma service with the cloud function service
  final service = GemmaService(
    cloudFunctionService: cloudFunctionService,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the Gemini Chat service (for chat, prompt suggestions, and note refresh)
final geminiChatServiceProvider = Provider<GeminiChatService>((ref) {
  // Get the cloud function service
  final cloudFunctionService = ref.watch(cloudFunctionServiceProvider);

  // Get the Remote Config providers
  final llmModelConfig = ref.watch(llmModelConfigProvider);

  // Create the Gemini Chat service with the cloud function service and Remote Config
  final service = GeminiChatService(
    cloudFunctionService: cloudFunctionService,
    llmModelConfig: llmModelConfig,
    // TODO: Add user tier detection here when subscription system is integrated
    userTier: null,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the Grok LLM service
final grokServiceProvider = Provider<GrokService>((ref) {
  // Get the cloud function service
  final cloudFunctionService = ref.watch(cloudFunctionServiceProvider);

  // Create the Grok service with the cloud function service
  final service = GrokService(
    cloudFunctionService: cloudFunctionService,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the LLM service (currently using Gemini)
final llmServiceProvider = Provider<LlmService>((ref) {
  return ref.watch(geminiServiceProvider);
});
