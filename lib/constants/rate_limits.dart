import 'package:voji/models/chat_rate_limit.dart';
import 'package:voji/utils/logger.dart';

/// Constants for rate limits
///
/// DEPRECATED: Rate limits are now managed via Firebase Remote Config
/// Use chatRateLimitsProvider instead for user-tier specific limits
class RateLimits {
  /// Default rate limits for chat messages
  /// DEPRECATED: Use chatRateLimitsProvider instead
  @Deprecated('Use chatRateLimitsProvider for user-tier specific limits')
  static final List<ChatRateLimit> defaultChatRateLimits = [
    // 20 messages per minute (pro tier default)
    const ChatRateLimit(
      maxMessages: 20,
      periodSeconds: 60,
      description: 'minute',
    ),
    // 1000 messages per day (pro tier default)
    const ChatRateLimit(
      maxMessages: 1000,
      periodSeconds: 86400, // 24 hours in seconds
      description: 'day',
    ),
    // 10000 messages per month (pro tier default)
    const ChatRateLimit(
      maxMessages: 10000,
      periodSeconds: 2592000, // 30 days in seconds
      description: 'month',
    ),
  ];

  /// Log the current rate limits for debugging
  static void logRateLimits() {
    Logger.debug('===== RATE LIMITS CONFIGURATION =====');
    for (final limit in defaultChatRateLimits) {
      Logger.debug('Rate limit: max ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds} seconds)');
    }
    Logger.debug('====================================');
  }
}
